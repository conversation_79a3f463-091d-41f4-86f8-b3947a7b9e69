"""
Technical analysis indicators for trading strategies
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import talib

class TechnicalIndicators:
    """Collection of technical analysis indicators"""
    
    @staticmethod
    def rsi(prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate Relative Strength Index"""
        return talib.RSI(prices.values, timeperiod=period)
    
    @staticmethod
    def ema(prices: pd.Series, period: int = 9) -> pd.Series:
        """Calculate Exponential Moving Average"""
        return talib.EMA(prices.values, timeperiod=period)
    
    @staticmethod
    def sma(prices: pd.Series, period: int = 20) -> pd.Series:
        """Calculate Simple Moving Average"""
        return talib.SMA(prices.values, timeperiod=period)
    
    @staticmethod
    def vwap(prices: pd.Series, volumes: pd.Series) -> pd.Series:
        """Calculate Volume Weighted Average Price"""
        return (prices * volumes).cumsum() / volumes.cumsum()
    
    @staticmethod
    def bollinger_bands(prices: pd.Series, period: int = 20, std_dev: float = 2.0) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate Bollinger Bands"""
        upper, middle, lower = talib.BBANDS(prices.values, timeperiod=period, nbdevup=std_dev, nbdevdn=std_dev)
        return pd.Series(upper), pd.Series(middle), pd.Series(lower)
    
    @staticmethod
    def atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """Calculate Average True Range"""
        return talib.ATR(high.values, low.values, close.values, timeperiod=period)
    
    @staticmethod
    def macd(prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate MACD"""
        macd_line, signal_line, histogram = talib.MACD(prices.values, fastperiod=fast, slowperiod=slow, signalperiod=signal)
        return pd.Series(macd_line), pd.Series(signal_line), pd.Series(histogram)
    
    @staticmethod
    def stochastic(high: pd.Series, low: pd.Series, close: pd.Series, k_period: int = 14, d_period: int = 3) -> Tuple[pd.Series, pd.Series]:
        """Calculate Stochastic Oscillator"""
        k_percent, d_percent = talib.STOCH(high.values, low.values, close.values, 
                                          fastk_period=k_period, slowk_period=d_period, slowd_period=d_period)
        return pd.Series(k_percent), pd.Series(d_percent)
    
    @staticmethod
    def volume_ratio(current_volume: float, avg_volume: float) -> float:
        """Calculate volume ratio vs average"""
        return current_volume / avg_volume if avg_volume > 0 else 0
    
    @staticmethod
    def price_change_percent(current_price: float, previous_price: float) -> float:
        """Calculate percentage price change"""
        return ((current_price - previous_price) / previous_price) * 100 if previous_price > 0 else 0

class MomentumAnalyzer:
    """Analyze momentum signals for breakout trading"""
    
    def __init__(self, rsi_period: int = 14, ema_period: int = 9, volume_lookback: int = 20):
        self.rsi_period = rsi_period
        self.ema_period = ema_period
        self.volume_lookback = volume_lookback
    
    def analyze_breakout(self, data: pd.DataFrame) -> Dict[str, any]:
        """
        Analyze if current conditions indicate a momentum breakout
        
        Args:
            data: DataFrame with columns ['open', 'high', 'low', 'close', 'volume']
        
        Returns:
            Dictionary with breakout analysis
        """
        if len(data) < max(self.rsi_period, self.ema_period, self.volume_lookback):
            return {'signal': 'INSUFFICIENT_DATA', 'confidence': 0.0}
        
        current = data.iloc[-1]
        previous = data.iloc[-2]
        
        # Calculate indicators
        rsi = TechnicalIndicators.rsi(data['close'], self.rsi_period)
        ema = TechnicalIndicators.ema(data['close'], self.ema_period)
        vwap = TechnicalIndicators.vwap(data['close'], data['volume'])
        
        # Volume analysis
        avg_volume = data['volume'].tail(self.volume_lookback).mean()
        volume_ratio = TechnicalIndicators.volume_ratio(current['volume'], avg_volume)
        
        # Price levels
        day_high = data['high'].max()
        day_low = data['low'].min()
        price_range = day_high - day_low
        
        # Breakout conditions
        current_rsi = rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50
        current_ema = ema.iloc[-1] if not pd.isna(ema.iloc[-1]) else current['close']
        current_vwap = vwap.iloc[-1] if not pd.isna(vwap.iloc[-1]) else current['close']
        
        # Bullish breakout signals
        bullish_signals = 0
        bullish_conditions = []
        
        if current['close'] > previous['high']:  # Breaking previous high
            bullish_signals += 2
            bullish_conditions.append("Price above previous high")
        
        if current['close'] > current_vwap:  # Above VWAP
            bullish_signals += 1
            bullish_conditions.append("Price above VWAP")
        
        if current['close'] > current_ema:  # Above EMA
            bullish_signals += 1
            bullish_conditions.append("Price above EMA")
        
        if current_rsi > 60:  # Strong momentum
            bullish_signals += 1
            bullish_conditions.append(f"RSI bullish ({current_rsi:.1f})")
        
        if volume_ratio > 1.5:  # High volume
            bullish_signals += 1
            bullish_conditions.append(f"High volume ({volume_ratio:.1f}x)")
        
        # Bearish breakout signals
        bearish_signals = 0
        bearish_conditions = []
        
        if current['close'] < previous['low']:  # Breaking previous low
            bearish_signals += 2
            bearish_conditions.append("Price below previous low")
        
        if current['close'] < current_vwap:  # Below VWAP
            bearish_signals += 1
            bearish_conditions.append("Price below VWAP")
        
        if current['close'] < current_ema:  # Below EMA
            bearish_signals += 1
            bearish_conditions.append("Price below EMA")
        
        if current_rsi < 40:  # Weak momentum
            bearish_signals += 1
            bearish_conditions.append(f"RSI bearish ({current_rsi:.1f})")
        
        if volume_ratio > 1.5:  # High volume (applies to both directions)
            bearish_signals += 1
            bearish_conditions.append(f"High volume ({volume_ratio:.1f}x)")
        
        # Determine signal
        min_signals_required = 3
        
        if bullish_signals >= min_signals_required and bullish_signals > bearish_signals:
            signal = 'BULLISH_BREAKOUT'
            confidence = min(bullish_signals / 5.0, 1.0)  # Max confidence of 1.0
            conditions = bullish_conditions
        elif bearish_signals >= min_signals_required and bearish_signals > bullish_signals:
            signal = 'BEARISH_BREAKOUT'
            confidence = min(bearish_signals / 5.0, 1.0)
            conditions = bearish_conditions
        else:
            signal = 'NO_SIGNAL'
            confidence = 0.0
            conditions = []
        
        return {
            'signal': signal,
            'confidence': confidence,
            'conditions': conditions,
            'indicators': {
                'rsi': current_rsi,
                'price': current['close'],
                'vwap': current_vwap,
                'ema': current_ema,
                'volume_ratio': volume_ratio,
                'price_change_pct': TechnicalIndicators.price_change_percent(current['close'], previous['close'])
            },
            'levels': {
                'day_high': day_high,
                'day_low': day_low,
                'previous_high': previous['high'],
                'previous_low': previous['low']
            }
        }

class VolatilityAnalyzer:
    """Analyze volatility for IV crush strategies"""
    
    def __init__(self, lookback_period: int = 30):
        self.lookback_period = lookback_period
    
    def calculate_realized_volatility(self, prices: pd.Series, periods: int = 252) -> float:
        """Calculate annualized realized volatility"""
        returns = prices.pct_change().dropna()
        return returns.std() * np.sqrt(periods)
    
    def calculate_iv_rank(self, current_iv: float, iv_history: pd.Series) -> float:
        """Calculate IV rank (percentile of current IV vs historical)"""
        if len(iv_history) == 0:
            return 0.5  # Default to middle rank if no history
        
        rank = (iv_history < current_iv).sum() / len(iv_history)
        return rank
    
    def analyze_iv_crush_opportunity(self, current_iv: float, iv_history: pd.Series, 
                                   price_data: pd.DataFrame) -> Dict[str, any]:
        """
        Analyze if conditions are favorable for IV crush strategy
        
        Args:
            current_iv: Current implied volatility
            iv_history: Historical IV data
            price_data: Recent price data for realized vol calculation
        
        Returns:
            Dictionary with IV crush analysis
        """
        if len(iv_history) < 10 or len(price_data) < 10:
            return {'signal': 'INSUFFICIENT_DATA', 'confidence': 0.0}
        
        # Calculate metrics
        iv_rank = self.calculate_iv_rank(current_iv, iv_history)
        realized_vol = self.calculate_realized_volatility(price_data['close'])
        iv_rv_ratio = current_iv / realized_vol if realized_vol > 0 else 1.0
        
        # Recent IV trend
        recent_iv = iv_history.tail(5)
        iv_trend = 'RISING' if recent_iv.iloc[-1] > recent_iv.mean() else 'FALLING'
        
        # Price stability (lower is better for IV crush)
        recent_returns = price_data['close'].pct_change().tail(10)
        price_stability = 1.0 - recent_returns.std()  # Higher = more stable
        
        # Scoring system
        score = 0
        conditions = []
        
        # High IV rank (want expensive options)
        if iv_rank > 0.7:
            score += 2
            conditions.append(f"High IV rank ({iv_rank:.1%})")
        elif iv_rank > 0.5:
            score += 1
            conditions.append(f"Moderate IV rank ({iv_rank:.1%})")
        
        # IV higher than realized vol
        if iv_rv_ratio > 1.2:
            score += 2
            conditions.append(f"IV > RV ({iv_rv_ratio:.1f}x)")
        elif iv_rv_ratio > 1.0:
            score += 1
            conditions.append(f"IV slightly > RV ({iv_rv_ratio:.1f}x)")
        
        # Falling IV trend (momentum for crush)
        if iv_trend == 'FALLING':
            score += 1
            conditions.append("IV trending down")
        
        # Price stability
        if price_stability > 0.8:
            score += 1
            conditions.append("Price stabilizing")
        
        # Determine signal
        if score >= 4:
            signal = 'STRONG_IV_CRUSH'
            confidence = min(score / 6.0, 1.0)
        elif score >= 2:
            signal = 'MODERATE_IV_CRUSH'
            confidence = score / 6.0
        else:
            signal = 'NO_SIGNAL'
            confidence = 0.0
        
        return {
            'signal': signal,
            'confidence': confidence,
            'conditions': conditions,
            'metrics': {
                'current_iv': current_iv,
                'iv_rank': iv_rank,
                'realized_vol': realized_vol,
                'iv_rv_ratio': iv_rv_ratio,
                'iv_trend': iv_trend,
                'price_stability': price_stability
            }
        }

class GammaAnalyzer:
    """Analyze gamma scalping opportunities"""
    
    def analyze_gamma_scalp_setup(self, option_data: Dict, underlying_data: pd.DataFrame) -> Dict[str, any]:
        """
        Analyze if conditions are good for gamma scalping
        
        Args:
            option_data: Dictionary with option Greeks and pricing
            underlying_data: Recent price data for the underlying
        
        Returns:
            Dictionary with gamma scalp analysis
        """
        if len(underlying_data) < 20:
            return {'signal': 'INSUFFICIENT_DATA', 'confidence': 0.0}
        
        # Extract option metrics
        gamma = option_data.get('gamma', 0)
        theta = option_data.get('theta', 0)
        vega = option_data.get('vega', 0)
        delta = option_data.get('delta', 0.5)
        
        # Analyze underlying price action
        recent_prices = underlying_data['close'].tail(20)
        price_volatility = recent_prices.pct_change().std()
        
        # Calculate price oscillation (good for gamma scalping)
        price_range = recent_prices.max() - recent_prices.min()
        current_price = recent_prices.iloc[-1]
        range_position = (current_price - recent_prices.min()) / price_range if price_range > 0 else 0.5
        
        # Trend strength (lower is better for gamma scalping)
        ema_short = TechnicalIndicators.ema(recent_prices, 5)
        ema_long = TechnicalIndicators.ema(recent_prices, 15)
        trend_strength = abs(ema_short.iloc[-1] - ema_long.iloc[-1]) / current_price
        
        # Scoring
        score = 0
        conditions = []
        
        # High gamma (essential for scalping)
        if gamma > 0.05:
            score += 3
            conditions.append(f"High gamma ({gamma:.3f})")
        elif gamma > 0.02:
            score += 1
            conditions.append(f"Moderate gamma ({gamma:.3f})")
        
        # Manageable theta decay
        if abs(theta) < 10:
            score += 1
            conditions.append(f"Low theta decay ({theta:.1f})")
        
        # Good volatility for oscillations
        if 0.01 < price_volatility < 0.03:
            score += 2
            conditions.append("Good price volatility for scalping")
        elif price_volatility > 0.005:
            score += 1
            conditions.append("Adequate price movement")
        
        # Not too trendy (want oscillations, not trends)
        if trend_strength < 0.005:
            score += 2
            conditions.append("Low trend strength (good for scalping)")
        elif trend_strength < 0.01:
            score += 1
            conditions.append("Moderate trend strength")
        
        # Near ATM (better gamma)
        if 0.4 < abs(delta) < 0.6:
            score += 1
            conditions.append("Near ATM (good delta)")
        
        # Determine signal
        if score >= 5:
            signal = 'STRONG_GAMMA_SCALP'
            confidence = min(score / 8.0, 1.0)
        elif score >= 3:
            signal = 'MODERATE_GAMMA_SCALP'
            confidence = score / 8.0
        else:
            signal = 'NO_SIGNAL'
            confidence = 0.0
        
        return {
            'signal': signal,
            'confidence': confidence,
            'conditions': conditions,
            'metrics': {
                'gamma': gamma,
                'theta': theta,
                'delta': delta,
                'vega': vega,
                'price_volatility': price_volatility,
                'trend_strength': trend_strength,
                'range_position': range_position
            }
        }
