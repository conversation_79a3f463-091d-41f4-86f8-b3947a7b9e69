# Same-Day Options Trading Program

A comprehensive intraday options trading system designed to generate consistent daily profits through automated strategy execution. This program implements multiple options trading strategies with robust risk management and real-time monitoring.

## 🎯 Objective

Generate approximately **$50 per day** profit on a **$30,000** capital base through same-day options trading, with no overnight positions to avoid gap risk.

## 🚀 Key Features

- **Multiple Trading Strategies**:
  - Momentum Breakout Scalping
  - IV Crush (Volatility Mean Reversion)
  - Gamma Scalping (Advanced)

- **Risk Management**:
  - Position sizing based on account risk
  - Stop-loss and profit targets
  - Daily loss limits
  - Real-time position monitoring

- **Real-time Data & Execution**:
  - Live market data feeds
  - Automated order execution via Alpaca API
  - Options chain analysis
  - Technical indicator calculations

- **Monitoring & Alerts**:
  - Real-time performance tracking
  - Slack notifications
  - Comprehensive logging
  - Daily performance reports

## 📋 Prerequisites

- Python 3.8+
- Alpaca Trading Account (Paper or Live)
- Optional: Polygon.io API key for enhanced data
- Optional: Slack webhook for notifications

## 🛠️ Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd same-day-options-trading
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Install TA-Lib** (Technical Analysis Library):
   
   **Windows**:
   ```bash
   # Download TA-Lib from: https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib
   pip install TA_Lib-0.4.28-cp39-cp39-win_amd64.whl  # Adjust for your Python version
   ```
   
   **macOS**:
   ```bash
   brew install ta-lib
   pip install TA-Lib
   ```
   
   **Linux**:
   ```bash
   sudo apt-get install libta-lib-dev
   pip install TA-Lib
   ```

4. **Set up environment variables**:
   Create a `.env` file in the project root:
   ```env
   # Alpaca API (Required)
   ALPACA_API_KEY=your_alpaca_api_key
   ALPACA_SECRET_KEY=your_alpaca_secret_key
   ALPACA_BASE_URL=https://paper-api.alpaca.markets  # Use paper trading initially

   # Optional APIs
   POLYGON_API_KEY=your_polygon_api_key
   FINNHUB_API_KEY=your_finnhub_api_key

   # Notifications (Optional)
   SLACK_WEBHOOK_URL=your_slack_webhook_url
   ```

## ⚙️ Configuration

Edit `config.py` to customize trading parameters:

```python
# Capital and Risk Management
TOTAL_CAPITAL = 30000.0
DAILY_PROFIT_TARGET = 50.0
MAX_DAILY_LOSS = 200.0
MAX_POSITION_RISK_PCT = 0.02  # 2% max risk per trade

# Strategy Allocation
MOMENTUM_ALLOCATION = 0.4   # 40% of capital
IV_CRUSH_ALLOCATION = 0.4   # 40% of capital
GAMMA_SCALP_ALLOCATION = 0.2  # 20% of capital
```

## 🏃‍♂️ Running the Program

### Option 1: Desktop GUI (Recommended)

1. **Start GUI Application**:
   ```bash
   python start_gui.py
   ```

   Or on Windows, double-click:
   ```
   start_gui.bat
   ```

2. **Using the GUI**:
   - Click "Start Trading" to begin automated trading
   - Monitor real-time performance in the dashboard
   - View positions, trades, and logs in separate tabs
   - Use "Emergency Stop" to immediately halt all trading

### Option 2: Command Line

1. **Test Configuration**:
   ```bash
   python config.py
   ```

2. **Start Trading Program**:
   ```bash
   python main.py
   ```

3. **Monitor Logs**:
   ```bash
   tail -f trading_log.log
   ```

## 📊 Trading Strategies

### 1. Momentum Breakout Scalping
- **Objective**: Capture quick profits from intraday momentum breakouts
- **Entry**: Technical breakout with volume confirmation
- **Exit**: 20% profit target or 20% stop loss
- **Hold Time**: Maximum 60 minutes

### 2. IV Crush Strategy
- **Objective**: Profit from volatility mean reversion
- **Entry**: High IV rank (>70th percentile) with stabilizing price action
- **Strategies**: Credit spreads, iron condors, short straddles/strangles
- **Exit**: 50% of maximum profit or 2x credit received loss

### 3. Gamma Scalping (Advanced)
- **Objective**: Delta-neutral volatility scalping
- **Entry**: High gamma options with oscillating underlying
- **Method**: Continuous delta hedging to capture price oscillations
- **Exit**: Accumulated small profits or trend emergence

## 🛡️ Risk Management

- **Position Sizing**: Maximum 2% of capital risk per trade
- **Daily Limits**: Stop trading at $50 profit or $200 loss
- **Time Management**: No trades in first 5 minutes or last 10 minutes
- **Liquidity Filters**: Only trade highly liquid options with tight spreads

## 📈 Performance Monitoring

The system provides comprehensive monitoring:

- **Real-time P&L tracking**
- **Win rate and trade statistics**
- **Risk metrics and drawdown analysis**
- **Strategy-specific performance**
- **Automated alerts and notifications**

## 🖥️ Desktop GUI Features

The desktop GUI provides a comprehensive interface for monitoring and controlling the trading system:

### Main Dashboard
- **Real-time P&L tracking** with color-coded indicators
- **Target progress bar** showing daily goal achievement
- **System status** with start/stop controls
- **Strategy performance** cards with individual metrics

### Performance Tab
- **Live performance chart** with target and loss limit lines
- **Historical P&L tracking** throughout the trading day
- **Interactive matplotlib charts** with zoom and pan

### Positions Tab
- **Current positions table** with real-time P&L updates
- **Position details** including entry price, current price, hold time
- **Color-coded P&L** (green for profits, red for losses)

### Trades Tab
- **Trade history** with complete transaction details
- **Entry and exit reasons** for each trade
- **Performance analysis** by strategy and symbol

### Logs Tab
- **Real-time system logs** with auto-scroll
- **Filterable log levels** (INFO, WARNING, ERROR)
- **Save logs** functionality for analysis

### Control Panel
- **Start/Stop trading** with safety confirmations
- **Emergency stop** for immediate position closure
- **Settings dialog** for configuration changes
- **Strategy status** monitoring

## 📁 Project Structure

```
same-day-options-trading/
├── start_gui.py           # GUI launcher script
├── start_gui.bat          # Windows GUI launcher
├── main.py                # Main application entry point
├── config.py              # Configuration settings
├── data_feeds.py          # Market data management
├── requirements.txt       # Python dependencies
├── gui/
│   ├── trading_gui.py     # Desktop GUI interface
│   └── launcher.py        # GUI-Trading integration
├── strategies/
│   ├── base_strategy.py   # Base strategy class
│   ├── momentum_breakout.py
│   ├── iv_crush.py
│   └── gamma_scalping.py  # (To be implemented)
├── execution/
│   ├── broker_api.py      # Broker integration
│   ├── order_manager.py   # Order management
│   └── risk_manager.py    # Risk management
├── utils/
│   ├── logger.py          # Logging system
│   ├── technical_indicators.py
│   ├── options_utils.py   # Options calculations
│   └── monitoring.py      # Real-time monitoring
└── README.md
```

## ⚠️ Important Disclaimers

1. **Paper Trading First**: Always test with paper trading before using real money
2. **Risk Warning**: Options trading involves significant risk of loss
3. **No Guarantees**: Past performance does not guarantee future results
4. **Regulatory Compliance**: Ensure compliance with local trading regulations
5. **Capital Requirements**: Only trade with capital you can afford to lose

## 🔧 Troubleshooting

### Common Issues:

1. **TA-Lib Installation Error**:
   - Ensure you have the correct version for your Python installation
   - On Windows, use pre-compiled wheels from Christoph Gohlke's site

2. **Alpaca API Connection Error**:
   - Verify API keys are correct
   - Check if using paper trading URL for testing

3. **Data Feed Issues**:
   - Ensure internet connection is stable
   - Check API rate limits

4. **Options Data Not Available**:
   - Some symbols may not have options
   - Check if market is open

## 📞 Support

For issues and questions:
1. Check the logs in `trading_log.log`
2. Review configuration in `config.py`
3. Ensure all API keys are properly set

## 📜 License

This project is for educational purposes. Use at your own risk.

## 🔄 Future Enhancements

- [ ] Web-based dashboard
- [ ] Machine learning signal enhancement
- [ ] Additional broker integrations
- [ ] Backtesting framework
- [ ] Portfolio optimization
- [ ] Advanced risk metrics

---

**Remember**: This is a sophisticated trading system. Start with paper trading, understand each component, and gradually scale up as you gain confidence in the system's performance.
