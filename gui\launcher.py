"""
GUI Launcher for Same-Day Options Trading Program
Integrates the desktop GUI with the trading engine
"""
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import asyncio
import queue
import sys
import os
from datetime import datetime
import time

# Add parent directory to path to import trading modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gui.trading_gui import TradingGUI
from main import TradingProgram
from utils.logger import trading_logger
from config import TRADING_CONFIG

class TradingGUILauncher:
    """Launcher that integrates GUI with trading program"""
    
    def __init__(self):
        self.gui = None
        self.trading_program = None
        self.trading_thread = None
        self.data_update_thread = None
        self.is_running = False
        
        # Communication queues
        self.gui_to_trading_queue = queue.Queue()
        self.trading_to_gui_queue = queue.Queue()
        
    def start_gui(self):
        """Start the GUI"""
        try:
            # Create enhanced GUI
            self.gui = EnhancedTradingGUI(self)
            self.gui.run()
            
        except Exception as e:
            print(f"Error starting GUI: {e}")
            import traceback
            traceback.print_exc()
    
    def start_trading_program(self):
        """Start the trading program in a separate thread"""
        try:
            if self.is_running:
                return False, "Trading program is already running"
            
            # Create trading program instance
            self.trading_program = TradingProgram()
            
            # Start trading in separate thread
            self.trading_thread = threading.Thread(target=self._run_trading_program, daemon=True)
            self.trading_thread.start()
            
            # Start data update thread
            self.data_update_thread = threading.Thread(target=self._data_update_loop, daemon=True)
            self.data_update_thread.start()
            
            self.is_running = True
            return True, "Trading program started successfully"
            
        except Exception as e:
            return False, f"Error starting trading program: {str(e)}"
    
    def stop_trading_program(self):
        """Stop the trading program"""
        try:
            if not self.is_running:
                return False, "Trading program is not running"
            
            self.is_running = False
            
            if self.trading_program:
                self.trading_program.shutdown_requested = True
            
            return True, "Trading program stop requested"
            
        except Exception as e:
            return False, f"Error stopping trading program: {str(e)}"
    
    def _run_trading_program(self):
        """Run the trading program (async wrapper)"""
        try:
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Run the trading program
            loop.run_until_complete(self._async_trading_program())
            
        except Exception as e:
            trading_logger.log_error(e, "Error in trading program thread")
        finally:
            self.is_running = False
    
    async def _async_trading_program(self):
        """Async trading program execution"""
        try:
            # Initialize trading program
            if not await self.trading_program.initialize():
                return
            
            # Run trading program
            await self.trading_program.run()
            
        except Exception as e:
            trading_logger.log_error(e, "Error in async trading program")
    
    def _data_update_loop(self):
        """Update GUI with trading program data"""
        while self.is_running:
            try:
                if self.trading_program and self.gui:
                    # Get current data from trading program
                    data = self._extract_trading_data()
                    
                    # Send to GUI
                    self.gui.data_queue.put(data)
                
                time.sleep(1)  # Update every second
                
            except Exception as e:
                trading_logger.log_error(e, "Error in data update loop")
                time.sleep(5)  # Wait longer on error
    
    def _extract_trading_data(self):
        """Extract data from trading program for GUI"""
        try:
            if not self.trading_program or not self.trading_program.strategies:
                return {}
            
            # Calculate totals
            total_pnl = sum(strategy.daily_pnl for strategy in self.trading_program.strategies)
            total_trades = sum(strategy.trades_today for strategy in self.trading_program.strategies)
            
            # Extract strategy data
            strategies = []
            for strategy in self.trading_program.strategies:
                strategies.append({
                    'name': strategy.name,
                    'is_active': strategy.is_active,
                    'pnl': strategy.daily_pnl,
                    'trades': strategy.trades_today,
                    'open_positions': len(strategy.open_positions),
                    'win_rate': strategy.win_rate
                })
            
            # Extract positions
            positions = []
            for strategy in self.trading_program.strategies:
                for pos in strategy.open_positions:
                    positions.append(pos.to_dict())
            
            # Extract recent trades (from closed positions)
            recent_trades = []
            for strategy in self.trading_program.strategies:
                for pos in strategy.closed_positions[-10:]:  # Last 10 trades
                    trade_data = pos.to_dict()
                    trade_data['action'] = 'CLOSE'
                    recent_trades.append(trade_data)
            
            # Create performance history point
            performance_point = {
                'timestamp': datetime.now().isoformat(),
                'total_pnl': total_pnl,
                'daily_high': max(total_pnl, 0),
                'daily_low': min(total_pnl, 0),
                'open_positions': len(positions),
                'total_trades': total_trades
            }
            
            return {
                'daily_pnl': total_pnl,
                'total_trades': total_trades,
                'open_positions': len(positions),
                'strategies': strategies,
                'positions': positions,
                'recent_trades': recent_trades,
                'performance_history': [performance_point],  # GUI will accumulate these
                'system_status': {
                    'is_running': self.is_running,
                    'last_update': datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            trading_logger.log_error(e, "Error extracting trading data")
            return {}

class EnhancedTradingGUI(TradingGUI):
    """Enhanced GUI with trading program integration"""
    
    def __init__(self, launcher):
        self.launcher = launcher
        self.performance_history_cache = []
        super().__init__()
    
    def start_trading(self):
        """Start the trading program"""
        try:
            success, message = self.launcher.start_trading_program()
            
            if success:
                self.is_running = True
                self.start_btn.config(state=tk.DISABLED)
                self.stop_btn.config(state=tk.NORMAL)
                self.emergency_btn.config(state=tk.NORMAL)
                
                self.log_message("Trading program started successfully")
                self.status_bar.config(text="Trading program running")
            else:
                self.log_message(f"Failed to start trading: {message}", "ERROR")
                messagebox.showerror("Error", message)
                
        except Exception as e:
            self.log_message(f"Error starting trading: {str(e)}", "ERROR")
            messagebox.showerror("Error", f"Failed to start trading: {str(e)}")
    
    def stop_trading(self):
        """Stop the trading program"""
        try:
            success, message = self.launcher.stop_trading_program()
            
            if success:
                self.is_running = False
                self.start_btn.config(state=tk.NORMAL)
                self.stop_btn.config(state=tk.DISABLED)
                self.emergency_btn.config(state=tk.DISABLED)
                
                self.log_message("Trading program stopped")
                self.status_bar.config(text="Trading program stopped")
            else:
                self.log_message(f"Failed to stop trading: {message}", "ERROR")
                messagebox.showerror("Error", message)
                
        except Exception as e:
            self.log_message(f"Error stopping trading: {str(e)}", "ERROR")
    
    def emergency_stop(self):
        """Emergency stop with immediate position closure"""
        try:
            result = messagebox.askyesno("Emergency Stop", 
                                       "This will immediately stop trading and close all positions. Continue?")
            if result:
                success, message = self.launcher.stop_trading_program()
                
                self.is_running = False
                self.start_btn.config(state=tk.NORMAL)
                self.stop_btn.config(state=tk.DISABLED)
                self.emergency_btn.config(state=tk.DISABLED)
                
                self.log_message("EMERGENCY STOP ACTIVATED", "ERROR")
                self.status_bar.config(text="EMERGENCY STOP - Trading halted")
                
                # In real implementation, would force close all positions
                if self.launcher.trading_program:
                    # Force shutdown
                    self.launcher.trading_program.shutdown_requested = True
                
        except Exception as e:
            self.log_message(f"Error in emergency stop: {str(e)}", "ERROR")
    
    def update_gui(self):
        """Enhanced GUI update with real trading data"""
        try:
            # Process any data updates from trading program
            while not self.data_queue.empty():
                try:
                    data = self.data_queue.get_nowait()
                    self.current_data.update(data)
                    
                    # Accumulate performance history
                    if 'performance_history' in data:
                        self.performance_history_cache.extend(data['performance_history'])
                        # Keep only last 1000 points
                        self.performance_history_cache = self.performance_history_cache[-1000:]
                        self.current_data['performance_history'] = self.performance_history_cache
                    
                except queue.Empty:
                    break
            
            # Update status indicators
            self.update_status_indicators()
            
            # Update strategy tree
            self.update_strategy_tree()
            
            # Update positions
            self.update_positions_tree()
            
            # Update trades
            self.update_trades_tree()
            
            # Update performance chart if visible
            if self.notebook.index(self.notebook.select()) == 0:
                self.update_performance_chart()
            
            # Update system status
            self.update_system_status()
            
        except Exception as e:
            self.log_message(f"GUI update error: {str(e)}", "ERROR")
    
    def update_system_status(self):
        """Update system status indicators"""
        try:
            # Check if trading program is actually running
            actual_running = self.launcher.is_running
            
            if self.is_running != actual_running:
                self.is_running = actual_running
                
                if not actual_running:
                    # Trading stopped unexpectedly
                    self.start_btn.config(state=tk.NORMAL)
                    self.stop_btn.config(state=tk.DISABLED)
                    self.emergency_btn.config(state=tk.DISABLED)
                    self.log_message("Trading program stopped unexpectedly", "WARNING")
            
        except Exception as e:
            self.log_message(f"Error updating system status: {str(e)}", "ERROR")
    
    def refresh_data(self):
        """Refresh data from trading program"""
        try:
            self.log_message("Refreshing data from trading program...")
            self.status_bar.config(text="Refreshing data...")
            
            # Force data update
            if self.launcher.trading_program:
                data = self.launcher._extract_trading_data()
                self.data_queue.put(data)
            
            self.status_bar.config(text="Data refreshed")
            
        except Exception as e:
            self.log_message(f"Error refreshing data: {str(e)}", "ERROR")
    
    def simulate_trading_data(self):
        """Override to prevent simulation when real trading is active"""
        if not self.launcher.is_running:
            # Only simulate if not actually trading
            super().simulate_trading_data()

def main():
    """Main entry point"""
    try:
        # Check if configuration is valid
        from config import validate_config
        validate_config()
        
        # Create and start launcher
        launcher = TradingGUILauncher()
        launcher.start_gui()
        
    except Exception as e:
        print(f"Error starting application: {e}")
        import traceback
        traceback.print_exc()
        
        # Show error dialog if possible
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("Startup Error", f"Failed to start application:\n{str(e)}")
        except:
            pass

if __name__ == "__main__":
    main()
