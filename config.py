"""
Configuration settings for the Same-Day Options Trading Program
"""
import os
from dataclasses import dataclass
from typing import List, Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

@dataclass
class TradingConfig:
    """Main trading configuration"""
    # Capital and Risk Management
    TOTAL_CAPITAL: float = 30000.0
    DAILY_PROFIT_TARGET: float = 50.0
    MAX_DAILY_LOSS: float = 200.0
    MAX_POSITION_RISK_PCT: float = 0.02  # 2% max risk per trade
    MAX_PORTFOLIO_RISK_PCT: float = 0.05  # 5% max total portfolio risk
    
    # Trading Universe
    LIQUID_STOCKS_COUNT: int = 1000
    MIN_MARKET_CAP: float = 100_000_000_000  # $100B
    MIN_OPTION_VOLUME: int = 100
    MAX_BID_ASK_SPREAD_PCT: float = 0.05  # 5% max spread
    
    # Strategy Allocation
    MOMENTUM_ALLOCATION: float = 0.4  # 40% of capital
    IV_CRUSH_ALLOCATION: float = 0.4   # 40% of capital
    GAMMA_SCALP_ALLOCATION: float = 0.2  # 20% of capital
    
    # Time Management
    MARKET_OPEN_DELAY_MINUTES: int = 5  # Wait 5 min after open
    MARKET_CLOSE_BUFFER_MINUTES: int = 10  # Close positions 10 min before close
    MAX_TRADE_DURATION_MINUTES: int = 60  # Max hold time per trade
    
    # API Keys (from environment variables)
    ALPACA_API_KEY: str = os.getenv('ALPACA_API_KEY', '')
    ALPACA_SECRET_KEY: str = os.getenv('ALPACA_SECRET_KEY', '')
    ALPACA_BASE_URL: str = os.getenv('ALPACA_BASE_URL', 'https://paper-api.alpaca.markets')
    
    POLYGON_API_KEY: str = os.getenv('POLYGON_API_KEY', '')
    FINNHUB_API_KEY: str = os.getenv('FINNHUB_API_KEY', '')
    
    # Notification settings
    SLACK_WEBHOOK_URL: str = os.getenv('SLACK_WEBHOOK_URL', '')
    ENABLE_NOTIFICATIONS: bool = True

@dataclass
class MomentumConfig:
    """Momentum Breakout Strategy Configuration"""
    # Entry Criteria
    MIN_VOLUME_MULTIPLIER: float = 2.0  # 2x average volume
    RSI_BULLISH_THRESHOLD: float = 70.0
    RSI_BEARISH_THRESHOLD: float = 30.0
    BREAKOUT_CONFIRMATION_PCT: float = 0.005  # 0.5% above resistance
    
    # Position Management
    PROFIT_TARGET_PCT: float = 0.20  # 20% option premium gain
    STOP_LOSS_PCT: float = 0.20     # 20% option premium loss
    MAX_CONTRACTS_PER_TRADE: int = 5
    
    # Option Selection
    PREFERRED_DTE: int = 0  # Same day expiry
    DELTA_RANGE: tuple = (0.45, 0.55)  # ATM options
    
    # Technical Indicators
    RSI_PERIOD: int = 14
    EMA_PERIOD: int = 9
    VOLUME_LOOKBACK_DAYS: int = 20

@dataclass
class IVCrushConfig:
    """IV Crush Strategy Configuration"""
    # Entry Criteria
    MIN_IV_RANK: float = 0.70  # 70th percentile
    MAX_IV_RANK: float = 0.95  # Don't trade if too extreme
    MIN_TIME_AFTER_EVENT_HOURS: int = 2  # Wait 2 hours after earnings/news
    
    # Position Management
    PROFIT_TARGET_PCT: float = 0.50  # Take 50% of max profit
    STOP_LOSS_MULTIPLIER: float = 2.0  # Stop if loss = 2x credit received
    MAX_LOSS_PER_TRADE: float = 500.0  # Max $500 loss per trade
    
    # Strategy Types
    ENABLE_STRADDLES: bool = True
    ENABLE_STRANGLES: bool = True
    ENABLE_IRON_CONDORS: bool = True
    ENABLE_CREDIT_SPREADS: bool = True
    
    # Strike Selection
    STRANGLE_DELTA: float = 0.20  # 20 delta for OTM strikes
    CONDOR_WIDTH: int = 10  # $10 wide condors
    SPREAD_WIDTH: int = 5   # $5 wide credit spreads

@dataclass
class GammaScalpConfig:
    """Gamma Scalping Strategy Configuration"""
    # Entry Criteria
    MIN_GAMMA: float = 0.05
    MAX_THETA: float = -10.0  # Limit theta decay exposure
    PREFERRED_SYMBOLS: List[str] = None  # Will default to ['SPY', 'QQQ', 'IWM']
    
    # Hedging Parameters
    DELTA_REBALANCE_THRESHOLD: float = 0.10  # Rebalance when delta > 10
    HEDGE_RATIO: float = 1.0  # 1:1 hedge ratio
    MIN_PRICE_MOVE_FOR_SCALP: float = 0.25  # Min $0.25 move to scalp
    
    # Risk Management
    MAX_GAMMA_EXPOSURE: float = 1000.0  # Max gamma exposure
    TREND_STOP_PCT: float = 0.02  # Stop if underlying trends 2%
    
    def __post_init__(self):
        if self.PREFERRED_SYMBOLS is None:
            self.PREFERRED_SYMBOLS = ['SPY', 'QQQ', 'IWM']

@dataclass
class MonitoringConfig:
    """Monitoring and Logging Configuration"""
    # Logging
    LOG_LEVEL: str = 'INFO'
    LOG_FILE: str = 'trading_log.log'
    MAX_LOG_SIZE_MB: int = 100
    LOG_BACKUP_COUNT: int = 5
    
    # Database
    DATABASE_FILE: str = 'trading_data.db'
    
    # Monitoring
    HEARTBEAT_INTERVAL_SECONDS: int = 30
    PERFORMANCE_UPDATE_INTERVAL_SECONDS: int = 60
    
    # Alerts
    ALERT_ON_TRADE_ENTRY: bool = True
    ALERT_ON_TRADE_EXIT: bool = True
    ALERT_ON_STOP_LOSS: bool = True
    ALERT_ON_DAILY_TARGET: bool = True
    ALERT_ON_SYSTEM_ERROR: bool = True

# Global configuration instances
TRADING_CONFIG = TradingConfig()
MOMENTUM_CONFIG = MomentumConfig()
IV_CRUSH_CONFIG = IVCrushConfig()
GAMMA_SCALP_CONFIG = GammaScalpConfig()
MONITORING_CONFIG = MonitoringConfig()

# Market hours (Eastern Time)
MARKET_HOURS = {
    'open': '09:30',
    'close': '16:00',
    'pre_market_start': '04:00',
    'after_hours_end': '20:00'
}

# Liquid stock universe (top 1000 by market cap and volume)
# This would typically be loaded from a data source
LIQUID_UNIVERSE = [
    'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'TSLA', 'META', 'BRK.B',
    'UNH', 'JNJ', 'XOM', 'JPM', 'V', 'PG', 'MA', 'HD', 'CVX', 'ABBV',
    'LLY', 'BAC', 'AVGO', 'KO', 'PEP', 'TMO', 'COST', 'MRK', 'WMT',
    'ADBE', 'ACN', 'MCD', 'CSCO', 'ABT', 'CRM', 'DHR', 'VZ', 'TXN',
    'NFLX', 'NKE', 'QCOM', 'PM', 'RTX', 'NEE', 'ORCL', 'DIS', 'WFC',
    'BMY', 'UPS', 'T', 'SPGI', 'LOW', 'HON', 'INTU', 'IBM', 'GS',
    'ELV', 'CAT', 'AMGN', 'SBUX', 'DE', 'AXP', 'BLK', 'SYK', 'BKNG',
    'MDLZ', 'GILD', 'ADP', 'TJX', 'VRTX', 'LRCX', 'ADI', 'MMC', 'CVS',
    'C', 'TMUS', 'MO', 'ZTS', 'SO', 'PLD', 'CB', 'ISRG', 'DUK', 'SHW',
    'CME', 'ITW', 'TGT', 'USB', 'BSX', 'AON', 'HCA', 'CL', 'APD', 'EMR',
    'GE', 'PYPL', 'MMM', 'EQIX', 'NSC', 'WM', 'FCX', 'SLB', 'PNC', 'GM'
    # ... would continue with full 1000 symbols
]

def validate_config():
    """Validate configuration settings"""
    errors = []
    
    if not TRADING_CONFIG.ALPACA_API_KEY:
        errors.append("ALPACA_API_KEY not set in environment variables")
    
    if not TRADING_CONFIG.ALPACA_SECRET_KEY:
        errors.append("ALPACA_SECRET_KEY not set in environment variables")
    
    if TRADING_CONFIG.MAX_DAILY_LOSS >= TRADING_CONFIG.DAILY_PROFIT_TARGET * 10:
        errors.append("MAX_DAILY_LOSS should be reasonable relative to profit target")
    
    total_allocation = (TRADING_CONFIG.MOMENTUM_ALLOCATION + 
                       TRADING_CONFIG.IV_CRUSH_ALLOCATION + 
                       TRADING_CONFIG.GAMMA_SCALP_ALLOCATION)
    if abs(total_allocation - 1.0) > 0.01:
        errors.append(f"Strategy allocations must sum to 1.0, got {total_allocation}")
    
    if errors:
        raise ValueError("Configuration errors:\n" + "\n".join(errors))
    
    return True

if __name__ == "__main__":
    try:
        validate_config()
        print("Configuration validation passed!")
    except ValueError as e:
        print(f"Configuration validation failed: {e}")
