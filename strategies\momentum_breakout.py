"""
Momentum Breakout Strategy Implementation
"""
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional
import pandas as pd

from strategies.base_strategy import BaseStrategy, Position
from utils.technical_indicators import MomentumAnalyzer
from utils.options_utils import OptionsCalculator, calculate_option_metrics
from utils.logger import trading_logger
from config import MOMENTUM_CONFIG, TRADING_CONFIG

class MomentumBreakoutStrategy(BaseStrategy):
    """
    Momentum breakout scalping strategy for intraday options trading
    
    This strategy identifies strong intraday momentum breakouts and takes
    directional options positions to capture quick price moves.
    """
    
    def __init__(self):
        super().__init__("MomentumBreakout", TRADING_CONFIG.MOMENTUM_ALLOCATION)
        self.momentum_analyzer = MomentumAnalyzer(
            rsi_period=MOMENTUM_CONFIG.RSI_PERIOD,
            ema_period=MOMENTUM_CONFIG.EMA_PERIOD,
            volume_lookback=MOMENTUM_CONFIG.VOLUME_LOOKBACK_DAYS
        )
        self.min_confidence = 0.6  # Minimum signal confidence to trade
        
    def generate_signals(self, market_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate momentum breakout signals
        
        Args:
            market_data: Dictionary with symbol data including OHLCV and options
            
        Returns:
            List of trading signals
        """
        signals = []
        
        for symbol, data in market_data.items():
            try:
                # Skip if insufficient data
                if 'price_data' not in data or len(data['price_data']) < 20:
                    continue
                
                # Skip if already have position in this symbol
                if any(pos.symbol == symbol for pos in self.open_positions):
                    continue
                
                price_df = pd.DataFrame(data['price_data'])
                
                # Analyze momentum breakout
                analysis = self.momentum_analyzer.analyze_breakout(price_df)
                
                if analysis['signal'] in ['BULLISH_BREAKOUT', 'BEARISH_BREAKOUT']:
                    if analysis['confidence'] >= self.min_confidence:
                        
                        # Additional filters
                        if self._passes_additional_filters(symbol, data, analysis):
                            signal = {
                                'symbol': symbol,
                                'signal_type': analysis['signal'],
                                'confidence': analysis['confidence'],
                                'direction': 'CALL' if analysis['signal'] == 'BULLISH_BREAKOUT' else 'PUT',
                                'current_price': analysis['indicators']['price'],
                                'entry_reason': ', '.join(analysis['conditions']),
                                'indicators': analysis['indicators'],
                                'levels': analysis['levels'],
                                'timestamp': datetime.now()
                            }
                            
                            signals.append(signal)
                            
                            trading_logger.log_signal(
                                strategy=self.name,
                                symbol=symbol,
                                signal_type=analysis['signal'],
                                confidence=analysis['confidence'],
                                indicators=analysis['indicators']
                            )
            
            except Exception as e:
                trading_logger.log_error(e, f"Error generating signal for {symbol}")
                continue
        
        return signals
    
    def _passes_additional_filters(self, symbol: str, data: Dict, analysis: Dict) -> bool:
        """Apply additional filters to momentum signals"""
        
        # Check if options data is available
        if 'options' not in data:
            return False
        
        # Check volume requirement
        volume_ratio = analysis['indicators'].get('volume_ratio', 0)
        if volume_ratio < MOMENTUM_CONFIG.MIN_VOLUME_MULTIPLIER:
            return False
        
        # Check RSI levels
        rsi = analysis['indicators'].get('rsi', 50)
        if analysis['signal_type'] == 'BULLISH_BREAKOUT':
            if rsi < MOMENTUM_CONFIG.RSI_BULLISH_THRESHOLD:
                return False
        else:  # BEARISH_BREAKOUT
            if rsi > MOMENTUM_CONFIG.RSI_BEARISH_THRESHOLD:
                return False
        
        # Check price confirmation
        price_change = analysis['indicators'].get('price_change_pct', 0)
        if abs(price_change) < MOMENTUM_CONFIG.BREAKOUT_CONFIRMATION_PCT * 100:
            return False
        
        # Check if we have suitable options
        suitable_options = self._find_suitable_options(data['options'], analysis['indicators']['price'])
        if not suitable_options:
            return False
        
        return True
    
    def _find_suitable_options(self, options_data: List[Dict], spot_price: float) -> List[Dict]:
        """Find suitable options for the trade"""
        suitable = []
        
        for option in options_data:
            # Check expiry (prefer same day)
            if option.get('dte', 999) > MOMENTUM_CONFIG.PREFERRED_DTE + 1:
                continue
            
            # Check delta range (prefer ATM)
            delta = abs(option.get('delta', 0))
            if not (MOMENTUM_CONFIG.DELTA_RANGE[0] <= delta <= MOMENTUM_CONFIG.DELTA_RANGE[1]):
                continue
            
            # Check liquidity
            if option.get('volume', 0) < 10 or option.get('bid', 0) <= 0:
                continue
            
            # Check bid-ask spread
            bid = option.get('bid', 0)
            ask = option.get('ask', 0)
            if ask > 0 and (ask - bid) / ask > 0.1:  # 10% max spread
                continue
            
            suitable.append(option)
        
        return suitable
    
    def calculate_position_size(self, signal: Dict[str, Any], option_price: float) -> int:
        """
        Calculate position size based on risk management
        
        Args:
            signal: Trading signal
            option_price: Current option price
            
        Returns:
            Number of contracts to trade
        """
        # Maximum risk per trade
        max_risk = self.allocated_capital * TRADING_CONFIG.MAX_POSITION_RISK_PCT
        
        # For long options, max loss is premium paid
        risk_per_contract = option_price * 100  # Contract multiplier
        
        if risk_per_contract <= 0:
            return 0
        
        # Calculate contracts based on risk
        contracts = int(max_risk / risk_per_contract)
        
        # Apply maximum contracts limit
        contracts = min(contracts, MOMENTUM_CONFIG.MAX_CONTRACTS_PER_TRADE)
        
        return max(1, contracts) if contracts > 0 else 0
    
    def get_entry_criteria(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get entry criteria for momentum breakout trade
        
        Args:
            signal: Trading signal
            
        Returns:
            Dictionary with entry parameters
        """
        current_price = signal['current_price']
        
        # Calculate stop loss and profit target based on option premium
        # We'll set these as percentages of option price since we don't have the exact option price here
        
        entry_criteria = {
            'order_type': 'LIMIT',  # Use limit orders to control entry price
            'limit_price_offset': 0.05,  # 5 cents above/below mid price
            'stop_loss_pct': MOMENTUM_CONFIG.STOP_LOSS_PCT,
            'profit_target_pct': MOMENTUM_CONFIG.PROFIT_TARGET_PCT,
            'max_hold_time_minutes': TRADING_CONFIG.MAX_TRADE_DURATION_MINUTES,
            'option_selection': {
                'type': signal['direction'],
                'delta_range': MOMENTUM_CONFIG.DELTA_RANGE,
                'max_dte': MOMENTUM_CONFIG.PREFERRED_DTE + 1,
                'min_volume': 10,
                'max_spread_pct': 0.10
            }
        }
        
        return entry_criteria
    
    def create_position_from_signal(self, signal: Dict[str, Any], option_data: Dict[str, Any], 
                                   contracts: int, entry_price: float) -> Position:
        """
        Create a position object from signal and option data
        
        Args:
            signal: Trading signal
            option_data: Selected option contract data
            contracts: Number of contracts
            entry_price: Entry price for the option
            
        Returns:
            Position object
        """
        # Calculate stop loss and profit target prices
        stop_loss_price = entry_price * (1 - MOMENTUM_CONFIG.STOP_LOSS_PCT)
        profit_target_price = entry_price * (1 + MOMENTUM_CONFIG.PROFIT_TARGET_PCT)
        
        position = Position(
            symbol=signal['symbol'],
            strategy=self.name,
            entry_time=datetime.now(),
            contracts=contracts,
            entry_price=entry_price,
            option_type=signal['direction'],
            strike=option_data['strike'],
            expiry=option_data['expiry'],
            stop_loss=stop_loss_price,
            profit_target=profit_target_price,
            max_hold_time=TRADING_CONFIG.MAX_TRADE_DURATION_MINUTES,
            delta=option_data.get('delta', 0),
            gamma=option_data.get('gamma', 0),
            theta=option_data.get('theta', 0),
            vega=option_data.get('vega', 0),
            iv=option_data.get('iv', 0),
            metadata={
                'signal_confidence': signal['confidence'],
                'entry_reason': signal['entry_reason'],
                'breakout_level': signal['levels'].get('previous_high' if signal['direction'] == 'CALL' else 'previous_low'),
                'volume_ratio': signal['indicators']['volume_ratio'],
                'rsi_at_entry': signal['indicators']['rsi']
            }
        )
        
        return position
    
    def should_close_early(self, position: Position, current_data: Dict[str, Any]) -> tuple:
        """
        Check if position should be closed early due to momentum failure
        
        Args:
            position: Current position
            current_data: Current market data
            
        Returns:
            Tuple of (should_close, reason)
        """
        if 'price_data' not in current_data:
            return False, ""
        
        try:
            price_df = pd.DataFrame(current_data['price_data'])
            current_analysis = self.momentum_analyzer.analyze_breakout(price_df)
            
            # Check if momentum has reversed
            if position.option_type == 'CALL':
                # For calls, check if we get bearish signals
                if current_analysis['signal'] == 'BEARISH_BREAKOUT':
                    if current_analysis['confidence'] > 0.5:
                        return True, "MOMENTUM_REVERSAL"
                
                # Check if price falls back below breakout level
                breakout_level = position.metadata.get('breakout_level', 0)
                current_price = current_analysis['indicators']['price']
                if current_price < breakout_level * 0.995:  # 0.5% buffer
                    return True, "BELOW_BREAKOUT_LEVEL"
            
            else:  # PUT
                # For puts, check if we get bullish signals
                if current_analysis['signal'] == 'BULLISH_BREAKOUT':
                    if current_analysis['confidence'] > 0.5:
                        return True, "MOMENTUM_REVERSAL"
                
                # Check if price rises back above breakdown level
                breakdown_level = position.metadata.get('breakout_level', 0)
                current_price = current_analysis['indicators']['price']
                if current_price > breakdown_level * 1.005:  # 0.5% buffer
                    return True, "ABOVE_BREAKDOWN_LEVEL"
            
            return False, ""
            
        except Exception as e:
            trading_logger.log_error(e, f"Error checking early exit for {position.symbol}")
            return False, ""
    
    def update_positions(self, market_data: Dict[str, Any]):
        """Update positions with current market data and check for exits"""
        super().update_positions(market_data)
        
        # Check for early exits due to momentum failure
        for position in self.open_positions:
            symbol_data = market_data.get(position.symbol, {})
            if symbol_data:
                should_close, reason = self.should_close_early(position, symbol_data)
                if should_close:
                    current_price = symbol_data.get('option_price', position.current_price)
                    self.close_position(position, current_price, reason)
