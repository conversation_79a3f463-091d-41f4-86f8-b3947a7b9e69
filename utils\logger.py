"""
Comprehensive logging system for the trading program
"""
import logging
import logging.handlers
import sqlite3
import json
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from pathlib import Path
import traceback

from config import MON<PERSON>ORING_CONFIG

class TradingLogger:
    """Enhanced logger for trading operations"""
    
    def __init__(self, name: str = "TradingProgram"):
        self.name = name
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, MONITORING_CONFIG.LOG_LEVEL))
        
        # Prevent duplicate handlers
        if not self.logger.handlers:
            self._setup_file_handler()
            self._setup_console_handler()
        
        # Initialize database for structured logging
        self._init_database()
    
    def _setup_file_handler(self):
        """Setup rotating file handler"""
        log_path = Path(MONITORING_CONFIG.LOG_FILE)
        log_path.parent.mkdir(exist_ok=True)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_path,
            maxBytes=MONITORING_CONFIG.MAX_LOG_SIZE_MB * 1024 * 1024,
            backupCount=MONITORING_CONFIG.LOG_BACKUP_COUNT
        )
        
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
    
    def _setup_console_handler(self):
        """Setup console handler for real-time monitoring"""
        console_handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
    
    def _init_database(self):
        """Initialize SQLite database for structured trade logging"""
        db_path = Path(MONITORING_CONFIG.DATABASE_FILE)
        db_path.parent.mkdir(exist_ok=True)
        
        self.conn = sqlite3.connect(db_path, check_same_thread=False)
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                strategy TEXT NOT NULL,
                symbol TEXT NOT NULL,
                action TEXT NOT NULL,
                quantity INTEGER,
                price REAL,
                option_type TEXT,
                strike REAL,
                expiry TEXT,
                iv REAL,
                delta REAL,
                gamma REAL,
                theta REAL,
                vega REAL,
                pnl REAL,
                reason TEXT,
                metadata TEXT
            )
        ''')
        
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS daily_performance (
                date TEXT PRIMARY KEY,
                total_pnl REAL,
                trades_count INTEGER,
                win_rate REAL,
                max_drawdown REAL,
                sharpe_ratio REAL,
                metadata TEXT
            )
        ''')
        
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS system_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                event_type TEXT NOT NULL,
                severity TEXT NOT NULL,
                message TEXT NOT NULL,
                metadata TEXT
            )
        ''')
        
        self.conn.commit()
    
    def log_trade(self, 
                  strategy: str,
                  symbol: str,
                  action: str,
                  quantity: Optional[int] = None,
                  price: Optional[float] = None,
                  option_type: Optional[str] = None,
                  strike: Optional[float] = None,
                  expiry: Optional[str] = None,
                  greeks: Optional[Dict[str, float]] = None,
                  pnl: Optional[float] = None,
                  reason: Optional[str] = None,
                  **metadata):
        """Log a trade with full details"""
        
        timestamp = datetime.now(timezone.utc).isoformat()
        
        # Extract Greeks
        iv = greeks.get('iv') if greeks else None
        delta = greeks.get('delta') if greeks else None
        gamma = greeks.get('gamma') if greeks else None
        theta = greeks.get('theta') if greeks else None
        vega = greeks.get('vega') if greeks else None
        
        # Log to file
        log_msg = f"TRADE - {strategy} | {symbol} | {action}"
        if quantity:
            log_msg += f" | Qty: {quantity}"
        if price:
            log_msg += f" | Price: ${price:.2f}"
        if pnl is not None:
            log_msg += f" | P&L: ${pnl:.2f}"
        if reason:
            log_msg += f" | Reason: {reason}"
        
        self.logger.info(log_msg)
        
        # Store in database
        try:
            self.conn.execute('''
                INSERT INTO trades (
                    timestamp, strategy, symbol, action, quantity, price,
                    option_type, strike, expiry, iv, delta, gamma, theta, vega,
                    pnl, reason, metadata
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                timestamp, strategy, symbol, action, quantity, price,
                option_type, strike, expiry, iv, delta, gamma, theta, vega,
                pnl, reason, json.dumps(metadata)
            ))
            self.conn.commit()
        except Exception as e:
            self.logger.error(f"Failed to log trade to database: {e}")
    
    def log_signal(self, strategy: str, symbol: str, signal_type: str, 
                   confidence: float, indicators: Dict[str, Any], **metadata):
        """Log trading signals"""
        log_msg = f"SIGNAL - {strategy} | {symbol} | {signal_type} | Confidence: {confidence:.2f}"
        
        # Add key indicators to log message
        if 'rsi' in indicators:
            log_msg += f" | RSI: {indicators['rsi']:.1f}"
        if 'price' in indicators:
            log_msg += f" | Price: ${indicators['price']:.2f}"
        if 'volume_ratio' in indicators:
            log_msg += f" | Vol Ratio: {indicators['volume_ratio']:.1f}x"
        
        self.logger.info(log_msg)
        
        # Store signal data in metadata for later analysis
        signal_data = {
            'strategy': strategy,
            'symbol': symbol,
            'signal_type': signal_type,
            'confidence': confidence,
            'indicators': indicators,
            **metadata
        }
        
        self._log_system_event('SIGNAL', 'INFO', log_msg, signal_data)
    
    def log_risk_event(self, event_type: str, symbol: str, current_risk: float, 
                       max_risk: float, action_taken: str, **metadata):
        """Log risk management events"""
        log_msg = f"RISK - {event_type} | {symbol} | Risk: {current_risk:.1f}% (Max: {max_risk:.1f}%) | Action: {action_taken}"
        self.logger.warning(log_msg)
        
        risk_data = {
            'event_type': event_type,
            'symbol': symbol,
            'current_risk': current_risk,
            'max_risk': max_risk,
            'action_taken': action_taken,
            **metadata
        }
        
        self._log_system_event('RISK', 'WARNING', log_msg, risk_data)
    
    def log_performance(self, daily_pnl: float, trades_count: int, 
                       win_rate: float, **metrics):
        """Log daily performance metrics"""
        date = datetime.now().strftime('%Y-%m-%d')
        
        log_msg = f"PERFORMANCE - Date: {date} | P&L: ${daily_pnl:.2f} | Trades: {trades_count} | Win Rate: {win_rate:.1%}"
        
        if 'sharpe_ratio' in metrics:
            log_msg += f" | Sharpe: {metrics['sharpe_ratio']:.2f}"
        if 'max_drawdown' in metrics:
            log_msg += f" | Max DD: {metrics['max_drawdown']:.1%}"
        
        self.logger.info(log_msg)
        
        # Store in database
        try:
            self.conn.execute('''
                INSERT OR REPLACE INTO daily_performance (
                    date, total_pnl, trades_count, win_rate, max_drawdown, sharpe_ratio, metadata
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                date, daily_pnl, trades_count, win_rate,
                metrics.get('max_drawdown'), metrics.get('sharpe_ratio'),
                json.dumps(metrics)
            ))
            self.conn.commit()
        except Exception as e:
            self.logger.error(f"Failed to log performance to database: {e}")
    
    def log_error(self, error: Exception, context: str = "", **metadata):
        """Log errors with full context"""
        error_msg = f"ERROR - {context} | {type(error).__name__}: {str(error)}"
        self.logger.error(error_msg)
        self.logger.error(f"Traceback: {traceback.format_exc()}")
        
        error_data = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'context': context,
            'traceback': traceback.format_exc(),
            **metadata
        }
        
        self._log_system_event('ERROR', 'ERROR', error_msg, error_data)
    
    def _log_system_event(self, event_type: str, severity: str, message: str, metadata: Dict):
        """Log system events to database"""
        timestamp = datetime.now(timezone.utc).isoformat()
        
        try:
            self.conn.execute('''
                INSERT INTO system_events (timestamp, event_type, severity, message, metadata)
                VALUES (?, ?, ?, ?, ?)
            ''', (timestamp, event_type, severity, message, json.dumps(metadata)))
            self.conn.commit()
        except Exception as e:
            self.logger.error(f"Failed to log system event to database: {e}")
    
    def get_trade_history(self, days: int = 30) -> list:
        """Retrieve trade history from database"""
        try:
            cursor = self.conn.execute('''
                SELECT * FROM trades 
                WHERE timestamp >= datetime('now', '-{} days')
                ORDER BY timestamp DESC
            '''.format(days))
            return cursor.fetchall()
        except Exception as e:
            self.logger.error(f"Failed to retrieve trade history: {e}")
            return []
    
    def get_performance_summary(self, days: int = 30) -> Dict[str, Any]:
        """Get performance summary from database"""
        try:
            cursor = self.conn.execute('''
                SELECT 
                    COUNT(*) as total_trades,
                    SUM(CASE WHEN pnl > 0 THEN 1 ELSE 0 END) as winning_trades,
                    SUM(pnl) as total_pnl,
                    AVG(pnl) as avg_pnl,
                    MAX(pnl) as max_win,
                    MIN(pnl) as max_loss
                FROM trades 
                WHERE timestamp >= datetime('now', '-{} days')
                AND pnl IS NOT NULL
            '''.format(days))
            
            result = cursor.fetchone()
            if result and result[0] > 0:
                return {
                    'total_trades': result[0],
                    'winning_trades': result[1],
                    'win_rate': result[1] / result[0] if result[0] > 0 else 0,
                    'total_pnl': result[2] or 0,
                    'avg_pnl': result[3] or 0,
                    'max_win': result[4] or 0,
                    'max_loss': result[5] or 0
                }
            else:
                return {
                    'total_trades': 0,
                    'winning_trades': 0,
                    'win_rate': 0,
                    'total_pnl': 0,
                    'avg_pnl': 0,
                    'max_win': 0,
                    'max_loss': 0
                }
        except Exception as e:
            self.logger.error(f"Failed to get performance summary: {e}")
            return {}
    
    def close(self):
        """Close database connection"""
        if hasattr(self, 'conn'):
            self.conn.close()

# Global logger instance
trading_logger = TradingLogger()

# Convenience functions
def log_trade(*args, **kwargs):
    trading_logger.log_trade(*args, **kwargs)

def log_signal(*args, **kwargs):
    trading_logger.log_signal(*args, **kwargs)

def log_risk_event(*args, **kwargs):
    trading_logger.log_risk_event(*args, **kwargs)

def log_performance(*args, **kwargs):
    trading_logger.log_performance(*args, **kwargs)

def log_error(*args, **kwargs):
    trading_logger.log_error(*args, **kwargs)
