"""
IV Crush Strategy Implementation
"""
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd

from strategies.base_strategy import BaseStrategy, Position
from utils.technical_indicators import VolatilityAnalyzer
from utils.options_utils import StrategyPricer, Op<PERSON><PERSON>hain
from utils.logger import trading_logger
from config import IV_CRUSH_CONFIG, TRADING_CONFIG

class IVCrushStrategy(BaseStrategy):
    """
    IV Crush strategy for capturing volatility mean reversion
    
    This strategy sells option premium when implied volatility is high
    and likely to revert downward, profiting from IV crush and theta decay.
    """
    
    def __init__(self):
        super().__init__("IVCrush", TRADING_CONFIG.IV_CRUSH_ALLOCATION)
        self.volatility_analyzer = VolatilityAnalyzer()
        self.min_confidence = 0.6
        
    def generate_signals(self, market_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate IV crush signals
        
        Args:
            market_data: Dictionary with symbol data including IV metrics
            
        Returns:
            List of trading signals
        """
        signals = []
        
        for symbol, data in market_data.items():
            try:
                # Skip if insufficient data
                if 'iv_data' not in data or 'price_data' not in data:
                    continue
                
                # Skip if already have position in this symbol
                if any(pos.symbol == symbol for pos in self.open_positions):
                    continue
                
                current_iv = data['iv_data'].get('current_iv', 0)
                iv_history = pd.Series(data['iv_data'].get('iv_history', []))
                price_df = pd.DataFrame(data['price_data'])
                
                # Analyze IV crush opportunity
                analysis = self.volatility_analyzer.analyze_iv_crush_opportunity(
                    current_iv, iv_history, price_df
                )
                
                if analysis['signal'] in ['STRONG_IV_CRUSH', 'MODERATE_IV_CRUSH']:
                    if analysis['confidence'] >= self.min_confidence:
                        
                        # Additional filters
                        if self._passes_iv_filters(symbol, data, analysis):
                            signal = {
                                'symbol': symbol,
                                'signal_type': analysis['signal'],
                                'confidence': analysis['confidence'],
                                'current_iv': current_iv,
                                'iv_rank': analysis['metrics']['iv_rank'],
                                'entry_reason': ', '.join(analysis['conditions']),
                                'metrics': analysis['metrics'],
                                'timestamp': datetime.now(),
                                'strategy_types': self._determine_strategy_types(data, analysis)
                            }
                            
                            signals.append(signal)
                            
                            trading_logger.log_signal(
                                strategy=self.name,
                                symbol=symbol,
                                signal_type=analysis['signal'],
                                confidence=analysis['confidence'],
                                indicators={'iv_rank': analysis['metrics']['iv_rank'], 'current_iv': current_iv}
                            )
            
            except Exception as e:
                trading_logger.log_error(e, f"Error generating IV crush signal for {symbol}")
                continue
        
        return signals
    
    def _passes_iv_filters(self, symbol: str, data: Dict, analysis: Dict) -> bool:
        """Apply additional filters to IV crush signals"""
        
        # Check IV rank threshold
        iv_rank = analysis['metrics']['iv_rank']
        if iv_rank < IV_CRUSH_CONFIG.MIN_IV_RANK:
            return False
        
        # Don't trade if IV is extremely high (might stay high)
        if iv_rank > IV_CRUSH_CONFIG.MAX_IV_RANK:
            return False
        
        # Check for upcoming events that might keep IV high
        if self._has_upcoming_events(symbol, data):
            return False
        
        # Check if options chain has suitable contracts
        if 'options' not in data:
            return False
        
        options_chain = OptionsChain(data['options'])
        liquid_options = options_chain.filter_by_liquidity(min_volume=50, max_spread_pct=0.08)
        
        if len(liquid_options.df) < 4:  # Need at least 4 options for spreads
            return False
        
        return True
    
    def _has_upcoming_events(self, symbol: str, data: Dict) -> bool:
        """Check if there are upcoming events that might keep IV high"""
        # This would typically check an events calendar
        # For now, we'll implement basic checks
        
        events = data.get('events', {})
        
        # Check for earnings within next few hours
        earnings_date = events.get('earnings_date')
        if earnings_date:
            try:
                earnings_dt = datetime.strptime(earnings_date, '%Y-%m-%d')
                hours_to_earnings = (earnings_dt - datetime.now()).total_seconds() / 3600
                if 0 < hours_to_earnings < IV_CRUSH_CONFIG.MIN_TIME_AFTER_EVENT_HOURS:
                    return True
            except:
                pass
        
        # Check for other major events
        if events.get('has_major_event_today', False):
            return True
        
        return False
    
    def _determine_strategy_types(self, data: Dict, analysis: Dict) -> List[str]:
        """Determine which IV crush strategy types to use"""
        strategy_types = []
        
        iv_rank = analysis['metrics']['iv_rank']
        price_stability = analysis['metrics']['price_stability']
        
        # High IV rank with good price stability - good for straddles/strangles
        if iv_rank > 0.8 and price_stability > 0.7:
            if IV_CRUSH_CONFIG.ENABLE_STRADDLES:
                strategy_types.append('SHORT_STRADDLE')
            if IV_CRUSH_CONFIG.ENABLE_STRANGLES:
                strategy_types.append('SHORT_STRANGLE')
        
        # Moderate IV with defined risk preference - use spreads
        if IV_CRUSH_CONFIG.ENABLE_CREDIT_SPREADS:
            strategy_types.append('CREDIT_SPREAD')
        
        if IV_CRUSH_CONFIG.ENABLE_IRON_CONDORS:
            strategy_types.append('IRON_CONDOR')
        
        return strategy_types
    
    def calculate_position_size(self, signal: Dict[str, Any], strategy_type: str, 
                              premium_received: float = None, max_loss: float = None) -> int:
        """
        Calculate position size for IV crush strategies
        
        Args:
            signal: Trading signal
            strategy_type: Type of strategy (CREDIT_SPREAD, IRON_CONDOR, etc.)
            premium_received: Premium received for short strategies
            max_loss: Maximum potential loss
            
        Returns:
            Number of contracts/spreads to trade
        """
        # Maximum risk per trade
        max_risk = min(
            self.allocated_capital * TRADING_CONFIG.MAX_POSITION_RISK_PCT,
            IV_CRUSH_CONFIG.MAX_LOSS_PER_TRADE
        )
        
        if strategy_type in ['SHORT_STRADDLE', 'SHORT_STRANGLE']:
            # For naked short strategies, use very conservative sizing
            # Risk is theoretically unlimited, so we size very small
            if premium_received and premium_received > 0:
                # Risk a multiple of premium received
                risk_per_contract = premium_received * IV_CRUSH_CONFIG.STOP_LOSS_MULTIPLIER * 100
                contracts = int(max_risk / risk_per_contract)
                return max(1, min(contracts, 2))  # Max 2 contracts for naked strategies
            else:
                return 1
        
        elif strategy_type in ['CREDIT_SPREAD', 'IRON_CONDOR']:
            # For defined risk strategies, use max loss
            if max_loss and max_loss > 0:
                contracts = int(max_risk / max_loss)
                return max(1, min(contracts, 10))  # Max 10 spreads
            else:
                return 1
        
        return 1
    
    def get_entry_criteria(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get entry criteria for IV crush trades
        
        Args:
            signal: Trading signal
            
        Returns:
            Dictionary with entry parameters
        """
        entry_criteria = {
            'order_type': 'LIMIT',
            'profit_target_pct': IV_CRUSH_CONFIG.PROFIT_TARGET_PCT,
            'stop_loss_multiplier': IV_CRUSH_CONFIG.STOP_LOSS_MULTIPLIER,
            'max_hold_time_minutes': TRADING_CONFIG.MAX_TRADE_DURATION_MINUTES,
            'strategy_types': signal['strategy_types'],
            'iv_threshold': {
                'min_rank': IV_CRUSH_CONFIG.MIN_IV_RANK,
                'max_rank': IV_CRUSH_CONFIG.MAX_IV_RANK
            }
        }
        
        return entry_criteria
    
    def create_spread_position(self, signal: Dict[str, Any], strategy_type: str,
                              options_data: Dict[str, Any], contracts: int) -> Position:
        """
        Create a spread position for IV crush strategy
        
        Args:
            signal: Trading signal
            strategy_type: Type of spread strategy
            options_data: Options chain data
            contracts: Number of spreads
            
        Returns:
            Position object
        """
        current_price = signal['metrics'].get('current_price', 0)
        
        if strategy_type == 'IRON_CONDOR':
            return self._create_iron_condor_position(signal, options_data, contracts)
        elif strategy_type == 'CREDIT_SPREAD':
            return self._create_credit_spread_position(signal, options_data, contracts)
        elif strategy_type == 'SHORT_STRADDLE':
            return self._create_straddle_position(signal, options_data, contracts)
        elif strategy_type == 'SHORT_STRANGLE':
            return self._create_strangle_position(signal, options_data, contracts)
        
        # Default fallback
        return None
    
    def _create_iron_condor_position(self, signal: Dict, options_data: Dict, contracts: int) -> Position:
        """Create iron condor position"""
        # This is a simplified implementation
        # In practice, you'd select 4 strikes and calculate the net credit
        
        spot_price = signal['metrics'].get('current_price', 100)
        
        # Select strikes (simplified)
        call_short_strike = spot_price + IV_CRUSH_CONFIG.CONDOR_WIDTH
        call_long_strike = call_short_strike + IV_CRUSH_CONFIG.CONDOR_WIDTH
        put_short_strike = spot_price - IV_CRUSH_CONFIG.CONDOR_WIDTH
        put_long_strike = put_short_strike - IV_CRUSH_CONFIG.CONDOR_WIDTH
        
        # Estimate credit (would be calculated from actual option prices)
        estimated_credit = 2.0  # Placeholder
        max_loss = IV_CRUSH_CONFIG.CONDOR_WIDTH - estimated_credit
        
        position = Position(
            symbol=signal['symbol'],
            strategy=self.name,
            entry_time=datetime.now(),
            contracts=contracts,
            entry_price=estimated_credit,
            option_type='IRON_CONDOR',
            strike=spot_price,  # Reference strike
            expiry=options_data.get('expiry', ''),
            profit_target=estimated_credit * (1 - IV_CRUSH_CONFIG.PROFIT_TARGET_PCT),
            stop_loss=estimated_credit * IV_CRUSH_CONFIG.STOP_LOSS_MULTIPLIER,
            max_hold_time=TRADING_CONFIG.MAX_TRADE_DURATION_MINUTES,
            metadata={
                'strategy_type': 'IRON_CONDOR',
                'call_short_strike': call_short_strike,
                'call_long_strike': call_long_strike,
                'put_short_strike': put_short_strike,
                'put_long_strike': put_long_strike,
                'max_loss': max_loss,
                'iv_rank_at_entry': signal['iv_rank'],
                'signal_confidence': signal['confidence']
            }
        )
        
        return position
    
    def _create_credit_spread_position(self, signal: Dict, options_data: Dict, contracts: int) -> Position:
        """Create credit spread position"""
        # Simplified implementation
        spot_price = signal['metrics'].get('current_price', 100)
        
        # Determine if call or put spread based on market conditions
        # For high IV, we might prefer put spreads if market is elevated
        spread_type = 'PUT_SPREAD'  # Simplified choice
        
        if spread_type == 'PUT_SPREAD':
            short_strike = spot_price - (spot_price * 0.02)  # 2% OTM
            long_strike = short_strike - IV_CRUSH_CONFIG.SPREAD_WIDTH
        else:  # CALL_SPREAD
            short_strike = spot_price + (spot_price * 0.02)  # 2% OTM
            long_strike = short_strike + IV_CRUSH_CONFIG.SPREAD_WIDTH
        
        estimated_credit = 1.5  # Placeholder
        max_loss = IV_CRUSH_CONFIG.SPREAD_WIDTH - estimated_credit
        
        position = Position(
            symbol=signal['symbol'],
            strategy=self.name,
            entry_time=datetime.now(),
            contracts=contracts,
            entry_price=estimated_credit,
            option_type=spread_type,
            strike=short_strike,
            expiry=options_data.get('expiry', ''),
            profit_target=estimated_credit * (1 - IV_CRUSH_CONFIG.PROFIT_TARGET_PCT),
            stop_loss=estimated_credit * IV_CRUSH_CONFIG.STOP_LOSS_MULTIPLIER,
            max_hold_time=TRADING_CONFIG.MAX_TRADE_DURATION_MINUTES,
            metadata={
                'strategy_type': 'CREDIT_SPREAD',
                'spread_type': spread_type,
                'short_strike': short_strike,
                'long_strike': long_strike,
                'max_loss': max_loss,
                'iv_rank_at_entry': signal['iv_rank'],
                'signal_confidence': signal['confidence']
            }
        )
        
        return position
    
    def _create_straddle_position(self, signal: Dict, options_data: Dict, contracts: int) -> Position:
        """Create short straddle position"""
        spot_price = signal['metrics'].get('current_price', 100)
        atm_strike = round(spot_price)
        
        # Estimate premium (would be sum of call and put premiums)
        estimated_premium = 4.0  # Placeholder
        
        position = Position(
            symbol=signal['symbol'],
            strategy=self.name,
            entry_time=datetime.now(),
            contracts=contracts,
            entry_price=estimated_premium,
            option_type='SHORT_STRADDLE',
            strike=atm_strike,
            expiry=options_data.get('expiry', ''),
            profit_target=estimated_premium * (1 - IV_CRUSH_CONFIG.PROFIT_TARGET_PCT),
            stop_loss=estimated_premium * IV_CRUSH_CONFIG.STOP_LOSS_MULTIPLIER,
            max_hold_time=TRADING_CONFIG.MAX_TRADE_DURATION_MINUTES,
            metadata={
                'strategy_type': 'SHORT_STRADDLE',
                'atm_strike': atm_strike,
                'iv_rank_at_entry': signal['iv_rank'],
                'signal_confidence': signal['confidence']
            }
        )
        
        return position
    
    def _create_strangle_position(self, signal: Dict, options_data: Dict, contracts: int) -> Position:
        """Create short strangle position"""
        spot_price = signal['metrics'].get('current_price', 100)
        
        # Select OTM strikes
        call_strike = spot_price * (1 + IV_CRUSH_CONFIG.STRANGLE_DELTA)
        put_strike = spot_price * (1 - IV_CRUSH_CONFIG.STRANGLE_DELTA)
        
        estimated_premium = 3.0  # Placeholder
        
        position = Position(
            symbol=signal['symbol'],
            strategy=self.name,
            entry_time=datetime.now(),
            contracts=contracts,
            entry_price=estimated_premium,
            option_type='SHORT_STRANGLE',
            strike=spot_price,  # Reference price
            expiry=options_data.get('expiry', ''),
            profit_target=estimated_premium * (1 - IV_CRUSH_CONFIG.PROFIT_TARGET_PCT),
            stop_loss=estimated_premium * IV_CRUSH_CONFIG.STOP_LOSS_MULTIPLIER,
            max_hold_time=TRADING_CONFIG.MAX_TRADE_DURATION_MINUTES,
            metadata={
                'strategy_type': 'SHORT_STRANGLE',
                'call_strike': call_strike,
                'put_strike': put_strike,
                'iv_rank_at_entry': signal['iv_rank'],
                'signal_confidence': signal['confidence']
            }
        )
        
        return position
    
    def should_close_early(self, position: Position, current_data: Dict[str, Any]) -> tuple:
        """
        Check if IV crush position should be closed early
        
        Args:
            position: Current position
            current_data: Current market data
            
        Returns:
            Tuple of (should_close, reason)
        """
        try:
            # Check if IV has increased significantly (against us)
            current_iv = current_data.get('iv_data', {}).get('current_iv', 0)
            entry_iv_rank = position.metadata.get('iv_rank_at_entry', 0)
            
            if current_iv > 0:
                iv_history = pd.Series(current_data.get('iv_data', {}).get('iv_history', []))
                if len(iv_history) > 0:
                    current_iv_rank = self.volatility_analyzer.calculate_iv_rank(current_iv, iv_history)
                    
                    # If IV rank increased significantly, consider closing
                    if current_iv_rank > entry_iv_rank + 0.2:  # 20 percentile points higher
                        return True, "IV_SPIKE"
            
            # Check for major price moves that might threaten short positions
            if 'price_data' in current_data:
                price_df = pd.DataFrame(current_data['price_data'])
                if len(price_df) > 0:
                    current_price = price_df['close'].iloc[-1]
                    entry_price = position.metadata.get('entry_price', current_price)
                    
                    price_move_pct = abs(current_price - entry_price) / entry_price
                    
                    # If price moved more than 3%, consider closing short vol positions
                    if price_move_pct > 0.03:
                        return True, "LARGE_PRICE_MOVE"
            
            return False, ""
            
        except Exception as e:
            trading_logger.log_error(e, f"Error checking early exit for IV crush position {position.symbol}")
            return False, ""
