#!/usr/bin/env python3
"""
Startup script for Same-Day Options Trading Program GUI
"""
import sys
import os
import subprocess

def check_requirements():
    """Check if basic requirements are met"""
    try:
        import tkinter
        print("✓ tkinter available")
    except ImportError:
        print("✗ tkinter not available - please install tkinter")
        return False
    
    try:
        import matplotlib
        print("✓ matplotlib available")
    except ImportError:
        print("✗ matplotlib not available - installing...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "matplotlib"])
            print("✓ matplotlib installed")
        except:
            print("✗ Failed to install matplotlib")
            return False
    
    try:
        import pandas
        print("✓ pandas available")
    except ImportError:
        print("✗ pandas not available - installing...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pandas"])
            print("✓ pandas installed")
        except:
            print("✗ Failed to install pandas")
            return False
    
    return True

def main():
    """Main startup function"""
    print("Same-Day Options Trading Program")
    print("=" * 40)
    print("Starting Desktop GUI...")
    
    # Check requirements
    if not check_requirements():
        print("\nPlease install missing requirements and try again.")
        input("Press Enter to exit...")
        return 1
    
    try:
        # Import and start GUI
        from gui.launcher import main as gui_main
        gui_main()
        
    except ImportError as e:
        print(f"\nImport error: {e}")
        print("Please ensure all files are in the correct location.")
        input("Press Enter to exit...")
        return 1
        
    except Exception as e:
        print(f"\nError starting GUI: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
