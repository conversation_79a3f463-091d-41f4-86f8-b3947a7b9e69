"""
Real-time market data feeds for the trading program
"""
import asyncio
import aiohttp
import pandas as pd
import yfinance as yf
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json
import time

from utils.logger import trading_logger
from utils.options_utils import calculate_option_metrics
from config import TRADING_CONFIG, LIQUID_UNIVERSE

class DataFeedManager:
    """Manages real-time market data feeds"""
    
    def __init__(self):
        self.alpaca_api_key = TRADING_CONFIG.ALPACA_API_KEY
        self.alpaca_secret = TRADING_CONFIG.ALPACA_SECRET_KEY
        self.polygon_api_key = TRADING_CONFIG.POLYGON_API_KEY
        self.finnhub_api_key = TRADING_CONFIG.FINNHUB_API_KEY
        
        self.session = None
        self.data_cache = {}
        self.last_update = {}
        
        # Data update intervals (seconds)
        self.price_update_interval = 30  # 30 seconds for price data
        self.options_update_interval = 60  # 1 minute for options data
        self.iv_update_interval = 300  # 5 minutes for IV data
    
    async def initialize(self):
        """Initialize async HTTP session"""
        self.session = aiohttp.ClientSession()
        trading_logger.logger.info("Data feed manager initialized")
    
    async def close(self):
        """Close HTTP session"""
        if self.session:
            await self.session.close()
    
    async def get_market_data(self, symbols: List[str]) -> Dict[str, Any]:
        """
        Get comprehensive market data for symbols
        
        Args:
            symbols: List of stock symbols
            
        Returns:
            Dictionary with market data for each symbol
        """
        market_data = {}
        
        # Limit to reasonable number of symbols to avoid API limits
        symbols = symbols[:100]  # Process max 100 symbols at once
        
        tasks = []
        for symbol in symbols:
            tasks.append(self._get_symbol_data(symbol))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for symbol, result in zip(symbols, results):
            if isinstance(result, Exception):
                trading_logger.log_error(result, f"Error fetching data for {symbol}")
                continue
            
            if result:
                market_data[symbol] = result
        
        return market_data
    
    async def _get_symbol_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get comprehensive data for a single symbol"""
        try:
            current_time = time.time()
            
            # Check if we need to update data
            last_update = self.last_update.get(symbol, 0)
            if current_time - last_update < self.price_update_interval:
                return self.data_cache.get(symbol)
            
            # Get price data
            price_data = await self._get_price_data(symbol)
            if not price_data:
                return None
            
            # Get options data
            options_data = await self._get_options_data(symbol)
            
            # Get IV data
            iv_data = await self._get_iv_data(symbol)
            
            # Get events data
            events_data = await self._get_events_data(symbol)
            
            symbol_data = {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'price_data': price_data,
                'options': options_data,
                'iv_data': iv_data,
                'events': events_data,
                'current_price': price_data[-1]['close'] if price_data else 0
            }
            
            # Cache the data
            self.data_cache[symbol] = symbol_data
            self.last_update[symbol] = current_time
            
            return symbol_data
            
        except Exception as e:
            trading_logger.log_error(e, f"Error getting symbol data for {symbol}")
            return None
    
    async def _get_price_data(self, symbol: str) -> List[Dict[str, Any]]:
        """Get intraday price data"""
        try:
            # Use yfinance for simplicity (in production, use real-time API)
            ticker = yf.Ticker(symbol)
            
            # Get 1-minute data for the last 2 hours
            data = ticker.history(period="1d", interval="1m")
            
            if data.empty:
                return []
            
            # Convert to list of dictionaries
            price_data = []
            for timestamp, row in data.iterrows():
                price_data.append({
                    'timestamp': timestamp.isoformat(),
                    'open': float(row['Open']),
                    'high': float(row['High']),
                    'low': float(row['Low']),
                    'close': float(row['Close']),
                    'volume': int(row['Volume'])
                })
            
            return price_data[-120:]  # Last 2 hours of data
            
        except Exception as e:
            trading_logger.log_error(e, f"Error getting price data for {symbol}")
            return []
    
    async def _get_options_data(self, symbol: str) -> List[Dict[str, Any]]:
        """Get options chain data"""
        try:
            # Use yfinance for options data (simplified)
            ticker = yf.Ticker(symbol)
            
            # Get options expiration dates
            expirations = ticker.options
            if not expirations:
                return []
            
            # Focus on nearest expiration (0DTE or next available)
            nearest_expiry = expirations[0]
            
            # Get options chain
            options_chain = ticker.option_chain(nearest_expiry)
            
            options_data = []
            
            # Process calls
            for _, row in options_chain.calls.iterrows():
                if row['volume'] > 0 and row['bid'] > 0:
                    options_data.append({
                        'symbol': symbol,
                        'type': 'CALL',
                        'strike': float(row['strike']),
                        'expiry': nearest_expiry,
                        'bid': float(row['bid']),
                        'ask': float(row['ask']),
                        'last': float(row['lastPrice']),
                        'volume': int(row['volume']),
                        'open_interest': int(row['openInterest']),
                        'iv': float(row['impliedVolatility']),
                        'delta': 0.5,  # Placeholder - would calculate actual Greeks
                        'gamma': 0.05,
                        'theta': -0.1,
                        'vega': 0.1,
                        'dte': self._calculate_dte(nearest_expiry)
                    })
            
            # Process puts
            for _, row in options_chain.puts.iterrows():
                if row['volume'] > 0 and row['bid'] > 0:
                    options_data.append({
                        'symbol': symbol,
                        'type': 'PUT',
                        'strike': float(row['strike']),
                        'expiry': nearest_expiry,
                        'bid': float(row['bid']),
                        'ask': float(row['ask']),
                        'last': float(row['lastPrice']),
                        'volume': int(row['volume']),
                        'open_interest': int(row['openInterest']),
                        'iv': float(row['impliedVolatility']),
                        'delta': -0.5,  # Placeholder
                        'gamma': 0.05,
                        'theta': -0.1,
                        'vega': 0.1,
                        'dte': self._calculate_dte(nearest_expiry)
                    })
            
            return options_data
            
        except Exception as e:
            trading_logger.log_error(e, f"Error getting options data for {symbol}")
            return []
    
    def _calculate_dte(self, expiry_str: str) -> int:
        """Calculate days to expiry"""
        try:
            expiry_date = datetime.strptime(expiry_str, '%Y-%m-%d')
            return (expiry_date - datetime.now()).days
        except:
            return 0
    
    async def _get_iv_data(self, symbol: str) -> Dict[str, Any]:
        """Get implied volatility data and history"""
        try:
            # This would typically come from a specialized options data provider
            # For now, we'll simulate IV data
            
            # Get current IV from options chain (simplified)
            current_iv = 0.25  # Placeholder 25% IV
            
            # Simulate IV history (would be from database or API)
            iv_history = [0.15, 0.18, 0.22, 0.28, 0.32, 0.29, 0.26, 0.24, 0.25]
            
            return {
                'current_iv': current_iv,
                'iv_history': iv_history,
                'iv_rank': len([iv for iv in iv_history if iv < current_iv]) / len(iv_history),
                'iv_percentile': current_iv,
                'hv_20': 0.20,  # 20-day historical volatility
                'hv_30': 0.22   # 30-day historical volatility
            }
            
        except Exception as e:
            trading_logger.log_error(e, f"Error getting IV data for {symbol}")
            return {}
    
    async def _get_events_data(self, symbol: str) -> Dict[str, Any]:
        """Get upcoming events data"""
        try:
            # This would typically come from an events calendar API
            # For now, return placeholder data
            
            return {
                'earnings_date': None,
                'has_major_event_today': False,
                'next_event': None,
                'event_type': None
            }
            
        except Exception as e:
            trading_logger.log_error(e, f"Error getting events data for {symbol}")
            return {}
    
    def get_liquid_universe(self) -> List[str]:
        """Get list of liquid stocks for trading"""
        # In production, this would be dynamically updated based on:
        # - Market cap
        # - Average volume
        # - Options volume
        # - Bid-ask spreads
        
        return LIQUID_UNIVERSE[:100]  # Return top 100 for now
    
    async def get_real_time_quote(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get real-time quote for a symbol"""
        try:
            # This would use a real-time data feed like Alpaca or Polygon
            # For now, use yfinance (delayed data)
            
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            return {
                'symbol': symbol,
                'price': info.get('currentPrice', 0),
                'bid': info.get('bid', 0),
                'ask': info.get('ask', 0),
                'volume': info.get('volume', 0),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            trading_logger.log_error(e, f"Error getting real-time quote for {symbol}")
            return None
    
    async def stream_market_data(self, symbols: List[str], callback):
        """Stream real-time market data"""
        # This would implement WebSocket streaming for real-time data
        # For now, we'll simulate with periodic updates
        
        while True:
            try:
                market_data = await self.get_market_data(symbols)
                await callback(market_data)
                await asyncio.sleep(30)  # Update every 30 seconds
                
            except Exception as e:
                trading_logger.log_error(e, "Error in market data stream")
                await asyncio.sleep(60)  # Wait longer on error

class MarketDataValidator:
    """Validates market data quality"""
    
    @staticmethod
    def validate_price_data(price_data: List[Dict]) -> bool:
        """Validate price data quality"""
        if not price_data or len(price_data) < 10:
            return False
        
        # Check for reasonable price ranges
        prices = [bar['close'] for bar in price_data]
        if max(prices) / min(prices) > 2.0:  # 100% move in session seems unrealistic
            return False
        
        # Check for zero volumes
        volumes = [bar['volume'] for bar in price_data]
        if sum(volumes) == 0:
            return False
        
        return True
    
    @staticmethod
    def validate_options_data(options_data: List[Dict]) -> bool:
        """Validate options data quality"""
        if not options_data:
            return False
        
        # Check for reasonable bid-ask spreads
        for option in options_data:
            if option['ask'] > 0 and option['bid'] > 0:
                spread_pct = (option['ask'] - option['bid']) / option['ask']
                if spread_pct > 0.5:  # 50% spread is too wide
                    continue
            else:
                continue
            
            # If we get here, we have at least one good option
            return True
        
        return False
    
    @staticmethod
    def validate_iv_data(iv_data: Dict) -> bool:
        """Validate IV data quality"""
        if not iv_data:
            return False
        
        current_iv = iv_data.get('current_iv', 0)
        if current_iv <= 0 or current_iv > 2.0:  # IV should be between 0 and 200%
            return False
        
        iv_history = iv_data.get('iv_history', [])
        if len(iv_history) < 5:  # Need some history
            return False
        
        return True

# Global data feed manager instance
data_feed_manager = DataFeedManager()

async def initialize_data_feeds():
    """Initialize data feeds"""
    await data_feed_manager.initialize()

async def cleanup_data_feeds():
    """Cleanup data feeds"""
    await data_feed_manager.close()

async def get_market_data_for_strategies(symbols: List[str] = None) -> Dict[str, Any]:
    """Get market data for trading strategies"""
    if symbols is None:
        symbols = data_feed_manager.get_liquid_universe()
    
    return await data_feed_manager.get_market_data(symbols)
