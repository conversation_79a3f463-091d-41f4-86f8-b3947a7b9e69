"""
Desktop GUI for Same-Day Options Trading Program
"""
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import asyncio
import queue
import time
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import pandas as pd

from config import TRADING_CONFIG
from utils.logger import trading_logger

class TradingGUI:
    """Desktop GUI for the trading program"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Same-Day Options Trading Program")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f0f0f0')

        # Data queues for thread-safe communication
        self.data_queue = queue.Queue()
        self.command_queue = queue.Queue()

        # Trading program reference
        self.trading_program = None
        self.is_running = False

        # GUI state
        self.current_data = {
            'daily_pnl': 0.0,
            'total_trades': 0,
            'open_positions': 0,
            'target_progress': 0.0,
            'strategies': [],
            'positions': [],
            'recent_trades': [],
            'performance_history': []
        }

        self.setup_gui()
        self.start_update_loop()

    def setup_gui(self):
        """Setup the main GUI layout"""
        # Create main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # Setup sections
        self.setup_header(main_frame)
        self.setup_control_panel(main_frame)
        self.setup_main_content(main_frame)
        self.setup_status_bar(main_frame)

    def setup_header(self, parent):
        """Setup header with status indicators"""
        header_frame = ttk.LabelFrame(parent, text="System Status", padding="10")
        header_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # Status indicators
        indicators_frame = ttk.Frame(header_frame)
        indicators_frame.pack(fill=tk.X)

        # Daily P&L
        pnl_frame = ttk.Frame(indicators_frame)
        pnl_frame.pack(side=tk.LEFT, padx=(0, 20))
        ttk.Label(pnl_frame, text="Daily P&L:", font=('Arial', 10, 'bold')).pack()
        self.pnl_label = ttk.Label(pnl_frame, text="$0.00", font=('Arial', 14, 'bold'), foreground='green')
        self.pnl_label.pack()

        # Target Progress
        progress_frame = ttk.Frame(indicators_frame)
        progress_frame.pack(side=tk.LEFT, padx=(0, 20))
        ttk.Label(progress_frame, text="Target Progress:", font=('Arial', 10, 'bold')).pack()
        self.progress_label = ttk.Label(progress_frame, text="0%", font=('Arial', 14, 'bold'), foreground='blue')
        self.progress_label.pack()

        # Total Trades
        trades_frame = ttk.Frame(indicators_frame)
        trades_frame.pack(side=tk.LEFT, padx=(0, 20))
        ttk.Label(trades_frame, text="Total Trades:", font=('Arial', 10, 'bold')).pack()
        self.trades_label = ttk.Label(trades_frame, text="0", font=('Arial', 14, 'bold'), foreground='purple')
        self.trades_label.pack()

        # System Status
        status_frame = ttk.Frame(indicators_frame)
        status_frame.pack(side=tk.LEFT, padx=(0, 20))
        ttk.Label(status_frame, text="System:", font=('Arial', 10, 'bold')).pack()
        self.status_label = ttk.Label(status_frame, text="STOPPED", font=('Arial', 14, 'bold'), foreground='red')
        self.status_label.pack()

        # Progress bar for target
        self.progress_bar = ttk.Progressbar(header_frame, length=300, mode='determinate')
        self.progress_bar.pack(pady=(10, 0))

    def setup_control_panel(self, parent):
        """Setup control panel"""
        control_frame = ttk.LabelFrame(parent, text="Control Panel", padding="10")
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))

        # Control buttons
        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack(fill=tk.X, pady=(0, 10))

        self.start_btn = ttk.Button(buttons_frame, text="Start Trading", command=self.start_trading)
        self.start_btn.pack(side=tk.TOP, fill=tk.X, pady=(0, 5))

        self.stop_btn = ttk.Button(buttons_frame, text="Stop Trading", command=self.stop_trading, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.TOP, fill=tk.X, pady=(0, 5))

        self.emergency_btn = ttk.Button(buttons_frame, text="Emergency Stop", command=self.emergency_stop, state=tk.DISABLED)
        self.emergency_btn.pack(side=tk.TOP, fill=tk.X, pady=(0, 10))

        ttk.Separator(buttons_frame, orient=tk.HORIZONTAL).pack(fill=tk.X, pady=(0, 10))

        self.refresh_btn = ttk.Button(buttons_frame, text="Refresh Data", command=self.refresh_data)
        self.refresh_btn.pack(side=tk.TOP, fill=tk.X, pady=(0, 5))

        self.settings_btn = ttk.Button(buttons_frame, text="Settings", command=self.open_settings)
        self.settings_btn.pack(side=tk.TOP, fill=tk.X, pady=(0, 5))

        # Strategy status
        strategy_frame = ttk.LabelFrame(control_frame, text="Strategy Status", padding="5")
        strategy_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        self.strategy_tree = ttk.Treeview(strategy_frame, columns=('Status', 'P&L', 'Trades'), show='tree headings', height=6)
        self.strategy_tree.heading('#0', text='Strategy')
        self.strategy_tree.heading('Status', text='Status')
        self.strategy_tree.heading('P&L', text='P&L')
        self.strategy_tree.heading('Trades', text='Trades')

        self.strategy_tree.column('#0', width=120)
        self.strategy_tree.column('Status', width=80)
        self.strategy_tree.column('P&L', width=80)
        self.strategy_tree.column('Trades', width=60)

        self.strategy_tree.pack(fill=tk.BOTH, expand=True)

    def setup_main_content(self, parent):
        """Setup main content area with tabs"""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(parent)
        self.notebook.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Performance tab
        self.setup_performance_tab()

        # Positions tab
        self.setup_positions_tab()

        # Trades tab
        self.setup_trades_tab()

        # Logs tab
        self.setup_logs_tab()

    def setup_performance_tab(self):
        """Setup performance chart tab"""
        perf_frame = ttk.Frame(self.notebook)
        self.notebook.add(perf_frame, text="Performance")

        # Create matplotlib figure
        self.fig = Figure(figsize=(10, 6), dpi=100)
        self.ax = self.fig.add_subplot(111)

        # Create canvas
        self.canvas = FigureCanvasTkAgg(self.fig, perf_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Initialize empty chart
        self.update_performance_chart()

    def setup_positions_tab(self):
        """Setup positions tab"""
        pos_frame = ttk.Frame(self.notebook)
        self.notebook.add(pos_frame, text="Positions")

        # Positions treeview
        self.positions_tree = ttk.Treeview(pos_frame,
                                         columns=('Strategy', 'Type', 'Strike', 'Contracts', 'Entry', 'Current', 'P&L', 'Time'),
                                         show='tree headings')

        # Configure columns
        columns = [
            ('#0', 'Symbol', 80),
            ('Strategy', 'Strategy', 100),
            ('Type', 'Type', 80),
            ('Strike', 'Strike', 80),
            ('Contracts', 'Qty', 60),
            ('Entry', 'Entry', 80),
            ('Current', 'Current', 80),
            ('P&L', 'P&L', 80),
            ('Time', 'Time', 80)
        ]

        for col_id, heading, width in columns:
            self.positions_tree.heading(col_id, text=heading)
            self.positions_tree.column(col_id, width=width)

        # Scrollbar for positions
        pos_scrollbar = ttk.Scrollbar(pos_frame, orient=tk.VERTICAL, command=self.positions_tree.yview)
        self.positions_tree.configure(yscrollcommand=pos_scrollbar.set)

        self.positions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        pos_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def setup_trades_tab(self):
        """Setup trades history tab"""
        trades_frame = ttk.Frame(self.notebook)
        self.notebook.add(trades_frame, text="Trades")

        # Trades treeview
        self.trades_tree = ttk.Treeview(trades_frame,
                                      columns=('Time', 'Strategy', 'Action', 'Type', 'Strike', 'Qty', 'Price', 'P&L', 'Reason'),
                                      show='tree headings')

        # Configure columns
        trade_columns = [
            ('#0', 'Symbol', 80),
            ('Time', 'Time', 80),
            ('Strategy', 'Strategy', 100),
            ('Action', 'Action', 60),
            ('Type', 'Type', 60),
            ('Strike', 'Strike', 70),
            ('Qty', 'Qty', 50),
            ('Price', 'Price', 70),
            ('P&L', 'P&L', 70),
            ('Reason', 'Reason', 120)
        ]

        for col_id, heading, width in trade_columns:
            self.trades_tree.heading(col_id, text=heading)
            self.trades_tree.column(col_id, width=width)

        # Scrollbar for trades
        trades_scrollbar = ttk.Scrollbar(trades_frame, orient=tk.VERTICAL, command=self.trades_tree.yview)
        self.trades_tree.configure(yscrollcommand=trades_scrollbar.set)

        self.trades_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        trades_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def setup_logs_tab(self):
        """Setup logs tab"""
        logs_frame = ttk.Frame(self.notebook)
        self.notebook.add(logs_frame, text="Logs")

        # Log text area
        self.log_text = scrolledtext.ScrolledText(logs_frame, wrap=tk.WORD, font=('Consolas', 9))
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # Log controls
        log_controls = ttk.Frame(logs_frame)
        log_controls.pack(fill=tk.X, pady=(5, 0))

        ttk.Button(log_controls, text="Clear Logs", command=self.clear_logs).pack(side=tk.LEFT)
        ttk.Button(log_controls, text="Save Logs", command=self.save_logs).pack(side=tk.LEFT, padx=(5, 0))

        # Auto-scroll checkbox
        self.auto_scroll_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(log_controls, text="Auto-scroll", variable=self.auto_scroll_var).pack(side=tk.RIGHT)

    def setup_status_bar(self, parent):
        """Setup status bar"""
        self.status_bar = ttk.Label(parent, text="Ready", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))

    def start_update_loop(self):
        """Start the GUI update loop"""
        self.update_gui()
        self.root.after(1000, self.start_update_loop)  # Update every second

    def update_gui(self):
        """Update GUI with latest data"""
        try:
            # Process any data updates
            while not self.data_queue.empty():
                try:
                    data = self.data_queue.get_nowait()
                    self.current_data.update(data)
                except queue.Empty:
                    break

            # Update status indicators
            self.update_status_indicators()

            # Update strategy tree
            self.update_strategy_tree()

            # Update positions
            self.update_positions_tree()

            # Update trades
            self.update_trades_tree()

            # Update performance chart
            if self.notebook.index(self.notebook.select()) == 0:  # Performance tab is active
                self.update_performance_chart()

        except Exception as e:
            self.log_message(f"GUI update error: {str(e)}", "ERROR")

    def update_status_indicators(self):
        """Update status indicators in header"""
        data = self.current_data

        # Update P&L
        pnl = data.get('daily_pnl', 0.0)
        self.pnl_label.config(text=f"${pnl:.2f}")
        self.pnl_label.config(foreground='green' if pnl >= 0 else 'red')

        # Update progress
        target_progress = (pnl / TRADING_CONFIG.DAILY_PROFIT_TARGET) * 100
        self.progress_label.config(text=f"{target_progress:.1f}%")
        self.progress_bar['value'] = max(0, min(100, target_progress))

        # Update trades
        self.trades_label.config(text=str(data.get('total_trades', 0)))

        # Update system status
        status = "RUNNING" if self.is_running else "STOPPED"
        self.status_label.config(text=status)
        self.status_label.config(foreground='green' if self.is_running else 'red')

    def update_strategy_tree(self):
        """Update strategy status tree"""
        # Clear existing items
        for item in self.strategy_tree.get_children():
            self.strategy_tree.delete(item)

        # Add strategy data
        strategies = self.current_data.get('strategies', [])
        for strategy in strategies:
            status = "Active" if strategy.get('is_active', False) else "Inactive"
            pnl = f"${strategy.get('pnl', 0.0):.2f}"
            trades = str(strategy.get('trades', 0))

            self.strategy_tree.insert('', 'end', text=strategy.get('name', 'Unknown'),
                                    values=(status, pnl, trades))

    def update_positions_tree(self):
        """Update positions tree"""
        # Clear existing items
        for item in self.positions_tree.get_children():
            self.positions_tree.delete(item)

        # Add position data
        positions = self.current_data.get('positions', [])
        for pos in positions:
            values = (
                pos.get('strategy', ''),
                pos.get('option_type', ''),
                f"{pos.get('strike', 0):.2f}",
                str(pos.get('contracts', 0)),
                f"${pos.get('entry_price', 0):.2f}",
                f"${pos.get('current_price', 0):.2f}",
                f"${pos.get('unrealized_pnl', 0):.2f}",
                f"{pos.get('hold_time_minutes', 0)}m"
            )

            item = self.positions_tree.insert('', 'end', text=pos.get('symbol', ''), values=values)

            # Color code by P&L
            pnl = pos.get('unrealized_pnl', 0)
            if pnl > 0:
                self.positions_tree.set(item, 'P&L', f"${pnl:.2f}")
            elif pnl < 0:
                self.positions_tree.set(item, 'P&L', f"${pnl:.2f}")

    def update_trades_tree(self):
        """Update trades tree"""
        # Clear existing items
        for item in self.trades_tree.get_children():
            self.trades_tree.delete(item)

        # Add recent trades
        trades = self.current_data.get('recent_trades', [])
        for trade in trades[-20:]:  # Show last 20 trades
            values = (
                trade.get('timestamp', ''),
                trade.get('strategy', ''),
                trade.get('action', ''),
                trade.get('option_type', ''),
                f"{trade.get('strike', 0):.2f}",
                str(trade.get('quantity', 0)),
                f"${trade.get('price', 0):.2f}",
                f"${trade.get('pnl', 0):.2f}" if trade.get('pnl') is not None else '',
                trade.get('reason', '')
            )

            self.trades_tree.insert('', 'end', text=trade.get('symbol', ''), values=values)

    def update_performance_chart(self):
        """Update performance chart"""
        self.ax.clear()

        # Get performance history
        history = self.current_data.get('performance_history', [])

        if not history:
            self.ax.text(0.5, 0.5, 'No data available', ha='center', va='center', transform=self.ax.transAxes)
            self.canvas.draw()
            return

        # Convert to DataFrame
        df = pd.DataFrame(history)
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])

            # Plot P&L line
            self.ax.plot(df['timestamp'], df['total_pnl'], 'b-', linewidth=2, label='P&L')

            # Add target and loss lines
            self.ax.axhline(y=TRADING_CONFIG.DAILY_PROFIT_TARGET, color='g', linestyle='--', alpha=0.7, label='Target')
            self.ax.axhline(y=-TRADING_CONFIG.MAX_DAILY_LOSS, color='r', linestyle='--', alpha=0.7, label='Loss Limit')

            self.ax.set_xlabel('Time')
            self.ax.set_ylabel('P&L ($)')
            self.ax.set_title('Daily Performance')
            self.ax.legend()
            self.ax.grid(True, alpha=0.3)

            # Format x-axis
            self.fig.autofmt_xdate()

        self.canvas.draw()

    def log_message(self, message, level="INFO"):
        """Add message to log display"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"

        self.log_text.insert(tk.END, log_entry)

        # Auto-scroll if enabled
        if self.auto_scroll_var.get():
            self.log_text.see(tk.END)

        # Color code by level
        if level == "ERROR":
            # Add red color tag (would need to implement text tags)
            pass
        elif level == "WARNING":
            # Add orange color tag
            pass

    # Control methods
    def start_trading(self):
        """Start the trading program"""
        try:
            self.is_running = True
            self.start_btn.config(state=tk.DISABLED)
            self.stop_btn.config(state=tk.NORMAL)
            self.emergency_btn.config(state=tk.NORMAL)

            self.log_message("Trading program started")
            self.status_bar.config(text="Trading program starting...")

            # In a real implementation, this would start the trading program in a separate thread
            # For now, we'll simulate with mock data
            self.simulate_trading_data()

        except Exception as e:
            self.log_message(f"Error starting trading: {str(e)}", "ERROR")
            messagebox.showerror("Error", f"Failed to start trading: {str(e)}")

    def stop_trading(self):
        """Stop the trading program"""
        try:
            self.is_running = False
            self.start_btn.config(state=tk.NORMAL)
            self.stop_btn.config(state=tk.DISABLED)
            self.emergency_btn.config(state=tk.DISABLED)

            self.log_message("Trading program stopped")
            self.status_bar.config(text="Trading program stopped")

        except Exception as e:
            self.log_message(f"Error stopping trading: {str(e)}", "ERROR")
            messagebox.showerror("Error", f"Failed to stop trading: {str(e)}")

    def emergency_stop(self):
        """Emergency stop - close all positions immediately"""
        try:
            result = messagebox.askyesno("Emergency Stop",
                                       "This will immediately close all positions and stop trading. Continue?")
            if result:
                self.is_running = False
                self.start_btn.config(state=tk.NORMAL)
                self.stop_btn.config(state=tk.DISABLED)
                self.emergency_btn.config(state=tk.DISABLED)

                self.log_message("EMERGENCY STOP ACTIVATED", "ERROR")
                self.status_bar.config(text="EMERGENCY STOP - All positions closed")

                # Clear positions (simulate closing all)
                self.current_data['positions'] = []

        except Exception as e:
            self.log_message(f"Error in emergency stop: {str(e)}", "ERROR")

    def refresh_data(self):
        """Refresh all data"""
        try:
            self.log_message("Refreshing data...")
            self.status_bar.config(text="Refreshing data...")

            # In real implementation, this would fetch fresh data
            self.simulate_trading_data()

            self.status_bar.config(text="Data refreshed")

        except Exception as e:
            self.log_message(f"Error refreshing data: {str(e)}", "ERROR")

    def open_settings(self):
        """Open settings dialog"""
        self.show_settings_dialog()

    def clear_logs(self):
        """Clear the log display"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("Logs cleared")

    def save_logs(self):
        """Save logs to file"""
        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, 'w') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                self.log_message(f"Logs saved to {filename}")

        except Exception as e:
            self.log_message(f"Error saving logs: {str(e)}", "ERROR")

    def show_settings_dialog(self):
        """Show settings configuration dialog"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title("Trading Settings")
        settings_window.geometry("500x600")
        settings_window.transient(self.root)
        settings_window.grab_set()

        # Create notebook for settings tabs
        settings_notebook = ttk.Notebook(settings_window)
        settings_notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # General settings tab
        general_frame = ttk.Frame(settings_notebook)
        settings_notebook.add(general_frame, text="General")

        # Capital settings
        ttk.Label(general_frame, text="Total Capital:").grid(row=0, column=0, sticky=tk.W, pady=5)
        capital_var = tk.StringVar(value=str(TRADING_CONFIG.TOTAL_CAPITAL))
        ttk.Entry(general_frame, textvariable=capital_var, width=15).grid(row=0, column=1, pady=5)

        ttk.Label(general_frame, text="Daily Profit Target:").grid(row=1, column=0, sticky=tk.W, pady=5)
        target_var = tk.StringVar(value=str(TRADING_CONFIG.DAILY_PROFIT_TARGET))
        ttk.Entry(general_frame, textvariable=target_var, width=15).grid(row=1, column=1, pady=5)

        ttk.Label(general_frame, text="Max Daily Loss:").grid(row=2, column=0, sticky=tk.W, pady=5)
        loss_var = tk.StringVar(value=str(TRADING_CONFIG.MAX_DAILY_LOSS))
        ttk.Entry(general_frame, textvariable=loss_var, width=15).grid(row=2, column=1, pady=5)

        # Risk settings tab
        risk_frame = ttk.Frame(settings_notebook)
        settings_notebook.add(risk_frame, text="Risk Management")

        ttk.Label(risk_frame, text="Max Position Risk %:").grid(row=0, column=0, sticky=tk.W, pady=5)
        risk_var = tk.StringVar(value=str(TRADING_CONFIG.MAX_POSITION_RISK_PCT * 100))
        ttk.Entry(risk_frame, textvariable=risk_var, width=15).grid(row=0, column=1, pady=5)

        # Strategy settings tab
        strategy_frame = ttk.Frame(settings_notebook)
        settings_notebook.add(strategy_frame, text="Strategies")

        ttk.Label(strategy_frame, text="Momentum Allocation %:").grid(row=0, column=0, sticky=tk.W, pady=5)
        momentum_var = tk.StringVar(value=str(TRADING_CONFIG.MOMENTUM_ALLOCATION * 100))
        ttk.Entry(strategy_frame, textvariable=momentum_var, width=15).grid(row=0, column=1, pady=5)

        ttk.Label(strategy_frame, text="IV Crush Allocation %:").grid(row=1, column=0, sticky=tk.W, pady=5)
        iv_var = tk.StringVar(value=str(TRADING_CONFIG.IV_CRUSH_ALLOCATION * 100))
        ttk.Entry(strategy_frame, textvariable=iv_var, width=15).grid(row=1, column=1, pady=5)

        # Buttons
        button_frame = ttk.Frame(settings_window)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(button_frame, text="Save", command=lambda: self.save_settings(settings_window)).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="Cancel", command=settings_window.destroy).pack(side=tk.RIGHT)

    def save_settings(self, window):
        """Save settings (placeholder)"""
        self.log_message("Settings saved (Note: Restart required for some changes)")
        window.destroy()

    def simulate_trading_data(self):
        """Simulate trading data for demo purposes"""
        import random

        # Simulate some trading activity
        strategies = [
            {
                'name': 'Momentum Breakout',
                'is_active': self.is_running,
                'pnl': random.uniform(-50, 100),
                'trades': random.randint(0, 5)
            },
            {
                'name': 'IV Crush',
                'is_active': self.is_running,
                'pnl': random.uniform(-30, 80),
                'trades': random.randint(0, 3)
            },
            {
                'name': 'Gamma Scalping',
                'is_active': False,
                'pnl': 0.0,
                'trades': 0
            }
        ]

        # Simulate positions
        positions = []
        if self.is_running:
            positions = [
                {
                    'symbol': 'AAPL',
                    'strategy': 'Momentum Breakout',
                    'option_type': 'CALL',
                    'strike': 150.0,
                    'contracts': 2,
                    'entry_price': 2.50,
                    'current_price': 3.25,
                    'unrealized_pnl': 150.0,
                    'hold_time_minutes': 25
                },
                {
                    'symbol': 'SPY',
                    'strategy': 'IV Crush',
                    'option_type': 'PUT_SPREAD',
                    'strike': 420.0,
                    'contracts': 1,
                    'entry_price': 1.50,
                    'current_price': 0.75,
                    'unrealized_pnl': 75.0,
                    'hold_time_minutes': 45
                }
            ]

        # Simulate recent trades
        recent_trades = [
            {
                'symbol': 'TSLA',
                'timestamp': '10:30:15',
                'strategy': 'Momentum Breakout',
                'action': 'CLOSE',
                'option_type': 'CALL',
                'strike': 200.0,
                'quantity': 1,
                'price': 3.75,
                'pnl': 125.0,
                'reason': 'PROFIT_TARGET'
            }
        ]

        # Update current data
        total_pnl = sum(s['pnl'] for s in strategies)
        total_trades = sum(s['trades'] for s in strategies)

        self.current_data.update({
            'daily_pnl': total_pnl,
            'total_trades': total_trades,
            'strategies': strategies,
            'positions': positions,
            'recent_trades': recent_trades
        })

    def run(self):
        """Run the GUI"""
        self.log_message("Same-Day Options Trading Program GUI Started")
        self.log_message(f"Target: ${TRADING_CONFIG.DAILY_PROFIT_TARGET}/day on ${TRADING_CONFIG.TOTAL_CAPITAL:,} capital")

        # Initialize with some demo data
        self.simulate_trading_data()

        # Start the GUI main loop
        self.root.mainloop()

def main():
    """Main entry point for GUI"""
    try:
        app = TradingGUI()
        app.run()
    except Exception as e:
        print(f"Error starting GUI: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()