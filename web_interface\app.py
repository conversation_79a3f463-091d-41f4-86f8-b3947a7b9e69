"""
Web Dashboard for Same-Day Options Trading Program
"""
import dash
from dash import dcc, html, Input, Output, State, dash_table
import plotly.graph_objs as go
import plotly.express as px
import pandas as pd
import json
from datetime import datetime, timedelta
import asyncio
import threading
import time

from utils.logger import trading_logger
from config import TRADING_CONFIG

class TradingDashboard:
    """Web-based trading dashboard"""
    
    def __init__(self, trading_program=None):
        self.trading_program = trading_program
        self.app = dash.Dash(__name__, external_stylesheets=['https://codepen.io/chriddyp/pen/bWLwgP.css'])
        self.setup_layout()
        self.setup_callbacks()
        
        # Data storage for real-time updates
        self.performance_data = []
        self.current_positions = []
        self.recent_trades = []
        self.system_status = {
            'is_running': False,
            'daily_pnl': 0.0,
            'total_trades': 0,
            'open_positions': 0,
            'last_update': datetime.now().isoformat()
        }
    
    def setup_layout(self):
        """Setup the dashboard layout"""
        self.app.layout = html.Div([
            # Header
            html.Div([
                html.H1("Same-Day Options Trading Dashboard", 
                       style={'textAlign': 'center', 'color': '#2c3e50', 'marginBottom': '30px'}),
                
                # Status indicators
                html.Div([
                    html.Div([
                        html.H3(id='daily-pnl', children='$0.00', 
                               style={'color': '#27ae60', 'margin': '0'}),
                        html.P('Daily P&L', style={'margin': '0', 'fontSize': '14px'})
                    ], className='three columns', style={'textAlign': 'center', 'backgroundColor': '#ecf0f1', 'padding': '20px', 'borderRadius': '5px'}),
                    
                    html.Div([
                        html.H3(id='target-progress', children='0%', 
                               style={'color': '#3498db', 'margin': '0'}),
                        html.P('Target Progress', style={'margin': '0', 'fontSize': '14px'})
                    ], className='three columns', style={'textAlign': 'center', 'backgroundColor': '#ecf0f1', 'padding': '20px', 'borderRadius': '5px'}),
                    
                    html.Div([
                        html.H3(id='total-trades', children='0', 
                               style={'color': '#9b59b6', 'margin': '0'}),
                        html.P('Total Trades', style={'margin': '0', 'fontSize': '14px'})
                    ], className='three columns', style={'textAlign': 'center', 'backgroundColor': '#ecf0f1', 'padding': '20px', 'borderRadius': '5px'}),
                    
                    html.Div([
                        html.H3(id='system-status', children='STOPPED', 
                               style={'color': '#e74c3c', 'margin': '0'}),
                        html.P('System Status', style={'margin': '0', 'fontSize': '14px'})
                    ], className='three columns', style={'textAlign': 'center', 'backgroundColor': '#ecf0f1', 'padding': '20px', 'borderRadius': '5px'}),
                ], className='row', style={'marginBottom': '30px'}),
            ]),
            
            # Control Panel
            html.Div([
                html.H3("Control Panel", style={'color': '#2c3e50'}),
                html.Div([
                    html.Button('Start Trading', id='start-btn', n_clicks=0, 
                               style={'backgroundColor': '#27ae60', 'color': 'white', 'border': 'none', 'padding': '10px 20px', 'marginRight': '10px', 'borderRadius': '5px'}),
                    html.Button('Stop Trading', id='stop-btn', n_clicks=0,
                               style={'backgroundColor': '#e74c3c', 'color': 'white', 'border': 'none', 'padding': '10px 20px', 'marginRight': '10px', 'borderRadius': '5px'}),
                    html.Button('Emergency Stop', id='emergency-btn', n_clicks=0,
                               style={'backgroundColor': '#c0392b', 'color': 'white', 'border': 'none', 'padding': '10px 20px', 'marginRight': '10px', 'borderRadius': '5px'}),
                    html.Button('Refresh Data', id='refresh-btn', n_clicks=0,
                               style={'backgroundColor': '#3498db', 'color': 'white', 'border': 'none', 'padding': '10px 20px', 'borderRadius': '5px'}),
                ], style={'marginBottom': '20px'}),
                
                html.Div(id='control-feedback', style={'color': '#27ae60', 'fontWeight': 'bold'})
            ], style={'backgroundColor': '#f8f9fa', 'padding': '20px', 'borderRadius': '5px', 'marginBottom': '30px'}),
            
            # Performance Chart
            html.Div([
                html.H3("Performance Chart", style={'color': '#2c3e50'}),
                dcc.Graph(id='performance-chart')
            ], style={'marginBottom': '30px'}),
            
            # Strategy Performance
            html.Div([
                html.H3("Strategy Performance", style={'color': '#2c3e50'}),
                html.Div(id='strategy-cards', className='row')
            ], style={'marginBottom': '30px'}),
            
            # Current Positions
            html.Div([
                html.H3("Current Positions", style={'color': '#2c3e50'}),
                html.Div(id='positions-table')
            ], style={'marginBottom': '30px'}),
            
            # Recent Trades
            html.Div([
                html.H3("Recent Trades", style={'color': '#2c3e50'}),
                html.Div(id='trades-table')
            ], style={'marginBottom': '30px'}),
            
            # System Logs
            html.Div([
                html.H3("System Logs", style={'color': '#2c3e50'}),
                html.Div(id='system-logs', 
                        style={'backgroundColor': '#2c3e50', 'color': '#ecf0f1', 'padding': '15px', 'borderRadius': '5px', 'fontFamily': 'monospace', 'fontSize': '12px', 'height': '300px', 'overflowY': 'scroll'})
            ]),
            
            # Auto-refresh interval
            dcc.Interval(
                id='interval-component',
                interval=5*1000,  # Update every 5 seconds
                n_intervals=0
            ),
            
            # Hidden div to store data
            html.Div(id='hidden-data', style={'display': 'none'})
        ], style={'margin': '20px'})
    
    def setup_callbacks(self):
        """Setup dashboard callbacks"""
        
        @self.app.callback(
            [Output('daily-pnl', 'children'),
             Output('daily-pnl', 'style'),
             Output('target-progress', 'children'),
             Output('total-trades', 'children'),
             Output('system-status', 'children'),
             Output('system-status', 'style')],
            [Input('interval-component', 'n_intervals')]
        )
        def update_status_indicators(n):
            """Update status indicators"""
            try:
                # Get current data
                data = self.get_current_data()
                
                daily_pnl = data.get('daily_pnl', 0.0)
                target_progress = (daily_pnl / TRADING_CONFIG.DAILY_PROFIT_TARGET) * 100
                total_trades = data.get('total_trades', 0)
                is_running = data.get('is_running', False)
                
                # Format P&L with color
                pnl_color = '#27ae60' if daily_pnl >= 0 else '#e74c3c'
                pnl_style = {'color': pnl_color, 'margin': '0'}
                
                # Format status
                status_text = 'RUNNING' if is_running else 'STOPPED'
                status_color = '#27ae60' if is_running else '#e74c3c'
                status_style = {'color': status_color, 'margin': '0'}
                
                return (
                    f'${daily_pnl:.2f}',
                    pnl_style,
                    f'{target_progress:.1f}%',
                    str(total_trades),
                    status_text,
                    status_style
                )
            except Exception as e:
                return '$0.00', {'color': '#e74c3c', 'margin': '0'}, '0%', '0', 'ERROR', {'color': '#e74c3c', 'margin': '0'}
        
        @self.app.callback(
            Output('performance-chart', 'figure'),
            [Input('interval-component', 'n_intervals')]
        )
        def update_performance_chart(n):
            """Update performance chart"""
            try:
                # Get performance history
                history = self.get_performance_history()
                
                if not history:
                    # Return empty chart
                    fig = go.Figure()
                    fig.add_annotation(text="No data available", x=0.5, y=0.5, showarrow=False)
                    fig.update_layout(title="Daily P&L Performance", xaxis_title="Time", yaxis_title="P&L ($)")
                    return fig
                
                df = pd.DataFrame(history)
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                
                fig = go.Figure()
                
                # Add P&L line
                fig.add_trace(go.Scatter(
                    x=df['timestamp'],
                    y=df['total_pnl'],
                    mode='lines+markers',
                    name='P&L',
                    line=dict(color='#3498db', width=2)
                ))
                
                # Add target line
                fig.add_hline(y=TRADING_CONFIG.DAILY_PROFIT_TARGET, 
                             line_dash="dash", line_color="#27ae60",
                             annotation_text="Daily Target")
                
                # Add loss limit line
                fig.add_hline(y=-TRADING_CONFIG.MAX_DAILY_LOSS, 
                             line_dash="dash", line_color="#e74c3c",
                             annotation_text="Loss Limit")
                
                fig.update_layout(
                    title="Daily P&L Performance",
                    xaxis_title="Time",
                    yaxis_title="P&L ($)",
                    hovermode='x unified',
                    showlegend=True
                )
                
                return fig
                
            except Exception as e:
                fig = go.Figure()
                fig.add_annotation(text=f"Error: {str(e)}", x=0.5, y=0.5, showarrow=False)
                return fig
        
        @self.app.callback(
            Output('strategy-cards', 'children'),
            [Input('interval-component', 'n_intervals')]
        )
        def update_strategy_cards(n):
            """Update strategy performance cards"""
            try:
                strategies_data = self.get_strategies_data()
                
                cards = []
                for strategy in strategies_data:
                    pnl_color = '#27ae60' if strategy['pnl'] >= 0 else '#e74c3c'
                    status_color = '#27ae60' if strategy['is_active'] else '#e74c3c'
                    
                    card = html.Div([
                        html.H4(strategy['name'], style={'margin': '0 0 10px 0', 'color': '#2c3e50'}),
                        html.P(f"P&L: ${strategy['pnl']:.2f}", style={'margin': '5px 0', 'color': pnl_color, 'fontWeight': 'bold'}),
                        html.P(f"Trades: {strategy['trades']}", style={'margin': '5px 0'}),
                        html.P(f"Open: {strategy['open_positions']}", style={'margin': '5px 0'}),
                        html.P(f"Win Rate: {strategy['win_rate']:.1%}", style={'margin': '5px 0'}),
                        html.P(f"Status: {'Active' if strategy['is_active'] else 'Inactive'}", 
                              style={'margin': '5px 0', 'color': status_color, 'fontWeight': 'bold'})
                    ], className='four columns', style={
                        'backgroundColor': '#f8f9fa', 
                        'padding': '15px', 
                        'borderRadius': '5px', 
                        'margin': '5px',
                        'border': f'2px solid {status_color}'
                    })
                    cards.append(card)
                
                return cards
                
            except Exception as e:
                return [html.Div(f"Error loading strategies: {str(e)}", style={'color': '#e74c3c'})]
        
        @self.app.callback(
            Output('positions-table', 'children'),
            [Input('interval-component', 'n_intervals')]
        )
        def update_positions_table(n):
            """Update positions table"""
            try:
                positions = self.get_current_positions()
                
                if not positions:
                    return html.P("No open positions", style={'color': '#7f8c8d', 'fontStyle': 'italic'})
                
                df = pd.DataFrame(positions)
                
                return dash_table.DataTable(
                    data=df.to_dict('records'),
                    columns=[
                        {'name': 'Symbol', 'id': 'symbol'},
                        {'name': 'Strategy', 'id': 'strategy'},
                        {'name': 'Type', 'id': 'option_type'},
                        {'name': 'Strike', 'id': 'strike', 'type': 'numeric', 'format': {'specifier': '.2f'}},
                        {'name': 'Contracts', 'id': 'contracts'},
                        {'name': 'Entry Price', 'id': 'entry_price', 'type': 'numeric', 'format': {'specifier': '.2f'}},
                        {'name': 'Current Price', 'id': 'current_price', 'type': 'numeric', 'format': {'specifier': '.2f'}},
                        {'name': 'P&L', 'id': 'unrealized_pnl', 'type': 'numeric', 'format': {'specifier': '.2f'}},
                        {'name': 'Hold Time', 'id': 'hold_time_minutes'},
                    ],
                    style_cell={'textAlign': 'center', 'fontSize': '12px'},
                    style_data_conditional=[
                        {
                            'if': {'filter_query': '{unrealized_pnl} > 0'},
                            'backgroundColor': '#d5f4e6',
                            'color': 'black',
                        },
                        {
                            'if': {'filter_query': '{unrealized_pnl} < 0'},
                            'backgroundColor': '#fadbd8',
                            'color': 'black',
                        }
                    ]
                )
                
            except Exception as e:
                return html.P(f"Error loading positions: {str(e)}", style={'color': '#e74c3c'})
        
        @self.app.callback(
            Output('trades-table', 'children'),
            [Input('interval-component', 'n_intervals')]
        )
        def update_trades_table(n):
            """Update recent trades table"""
            try:
                trades = self.get_recent_trades()
                
                if not trades:
                    return html.P("No recent trades", style={'color': '#7f8c8d', 'fontStyle': 'italic'})
                
                df = pd.DataFrame(trades)
                
                return dash_table.DataTable(
                    data=df.to_dict('records'),
                    columns=[
                        {'name': 'Time', 'id': 'timestamp'},
                        {'name': 'Symbol', 'id': 'symbol'},
                        {'name': 'Strategy', 'id': 'strategy'},
                        {'name': 'Action', 'id': 'action'},
                        {'name': 'Type', 'id': 'option_type'},
                        {'name': 'Strike', 'id': 'strike', 'type': 'numeric', 'format': {'specifier': '.2f'}},
                        {'name': 'Contracts', 'id': 'quantity'},
                        {'name': 'Price', 'id': 'price', 'type': 'numeric', 'format': {'specifier': '.2f'}},
                        {'name': 'P&L', 'id': 'pnl', 'type': 'numeric', 'format': {'specifier': '.2f'}},
                        {'name': 'Reason', 'id': 'reason'},
                    ],
                    style_cell={'textAlign': 'center', 'fontSize': '11px'},
                    page_size=10
                )
                
            except Exception as e:
                return html.P(f"Error loading trades: {str(e)}", style={'color': '#e74c3c'})
        
        @self.app.callback(
            Output('system-logs', 'children'),
            [Input('interval-component', 'n_intervals')]
        )
        def update_system_logs(n):
            """Update system logs"""
            try:
                logs = self.get_recent_logs()
                return logs
            except Exception as e:
                return f"Error loading logs: {str(e)}"
        
        @self.app.callback(
            Output('control-feedback', 'children'),
            [Input('start-btn', 'n_clicks'),
             Input('stop-btn', 'n_clicks'),
             Input('emergency-btn', 'n_clicks'),
             Input('refresh-btn', 'n_clicks')]
        )
        def handle_controls(start_clicks, stop_clicks, emergency_clicks, refresh_clicks):
            """Handle control button clicks"""
            ctx = dash.callback_context
            
            if not ctx.triggered:
                return ""
            
            button_id = ctx.triggered[0]['prop_id'].split('.')[0]
            
            if button_id == 'start-btn':
                return "Start command sent (Note: Manual start required in console)"
            elif button_id == 'stop-btn':
                return "Stop command sent (Note: Manual stop required in console)"
            elif button_id == 'emergency-btn':
                return "Emergency stop command sent (Note: Manual intervention required)"
            elif button_id == 'refresh-btn':
                return "Data refreshed"
            
            return ""
    
    def get_current_data(self):
        """Get current system data"""
        # This would interface with the actual trading program
        # For now, return mock data
        return {
            'daily_pnl': 25.50,
            'total_trades': 3,
            'open_positions': 2,
            'is_running': True,
            'last_update': datetime.now().isoformat()
        }
    
    def get_performance_history(self):
        """Get performance history"""
        # Mock data - in real implementation, this would come from the trading program
        now = datetime.now()
        history = []
        
        for i in range(50):
            timestamp = now - timedelta(minutes=i*5)
            pnl = 25.50 - (i * 0.5) + (i % 10) * 2
            history.append({
                'timestamp': timestamp.isoformat(),
                'total_pnl': pnl,
                'daily_high': max(25.50, pnl),
                'daily_low': min(0, pnl),
                'open_positions': 2,
                'total_trades': 3
            })
        
        return list(reversed(history))
    
    def get_strategies_data(self):
        """Get strategies performance data"""
        return [
            {
                'name': 'Momentum Breakout',
                'pnl': 15.25,
                'trades': 2,
                'open_positions': 1,
                'win_rate': 0.75,
                'is_active': True
            },
            {
                'name': 'IV Crush',
                'pnl': 10.25,
                'trades': 1,
                'open_positions': 1,
                'win_rate': 1.0,
                'is_active': True
            },
            {
                'name': 'Gamma Scalping',
                'pnl': 0.0,
                'trades': 0,
                'open_positions': 0,
                'win_rate': 0.0,
                'is_active': False
            }
        ]
    
    def get_current_positions(self):
        """Get current positions"""
        return [
            {
                'symbol': 'AAPL',
                'strategy': 'Momentum Breakout',
                'option_type': 'CALL',
                'strike': 150.0,
                'contracts': 2,
                'entry_price': 2.50,
                'current_price': 3.25,
                'unrealized_pnl': 150.0,
                'hold_time_minutes': 25
            },
            {
                'symbol': 'SPY',
                'strategy': 'IV Crush',
                'option_type': 'PUT_SPREAD',
                'strike': 420.0,
                'contracts': 1,
                'entry_price': 1.50,
                'current_price': 0.75,
                'unrealized_pnl': 75.0,
                'hold_time_minutes': 45
            }
        ]
    
    def get_recent_trades(self):
        """Get recent trades"""
        return [
            {
                'timestamp': '10:30:15',
                'symbol': 'TSLA',
                'strategy': 'Momentum Breakout',
                'action': 'CLOSE',
                'option_type': 'CALL',
                'strike': 200.0,
                'quantity': 1,
                'price': 3.75,
                'pnl': 125.0,
                'reason': 'PROFIT_TARGET'
            },
            {
                'timestamp': '10:15:30',
                'symbol': 'AAPL',
                'strategy': 'Momentum Breakout',
                'action': 'OPEN',
                'option_type': 'CALL',
                'strike': 150.0,
                'quantity': 2,
                'price': 2.50,
                'pnl': None,
                'reason': 'BULLISH_BREAKOUT'
            }
        ]
    
    def get_recent_logs(self):
        """Get recent system logs"""
        logs = [
            f"{datetime.now().strftime('%H:%M:%S')} - INFO - Trading cycle completed successfully",
            f"{datetime.now().strftime('%H:%M:%S')} - INFO - Momentum strategy generated 1 signal for AAPL",
            f"{datetime.now().strftime('%H:%M:%S')} - INFO - Position opened: 2 CALL AAPL 150",
            f"{datetime.now().strftime('%H:%M:%S')} - INFO - Daily P&L: $25.50",
            f"{datetime.now().strftime('%H:%M:%S')} - INFO - System heartbeat OK"
        ]
        
        return html.Pre('\n'.join(logs))
    
    def run(self, host='127.0.0.1', port=8050, debug=False):
        """Run the dashboard"""
        print(f"Starting Trading Dashboard at http://{host}:{port}")
        self.app.run_server(host=host, port=port, debug=debug)

# Create dashboard instance
dashboard = TradingDashboard()

if __name__ == '__main__':
    dashboard.run(debug=True)
