"""
Base strategy class for all trading strategies
"""
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import pandas as pd

from utils.logger import trading_logger
from config import TRADING_CONFIG

class Position:
    """Represents a trading position"""
    
    def __init__(self, symbol: str, strategy: str, entry_time: datetime,
                 contracts: int, entry_price: float, option_type: str,
                 strike: float, expiry: str, **kwargs):
        self.symbol = symbol
        self.strategy = strategy
        self.entry_time = entry_time
        self.contracts = contracts
        self.entry_price = entry_price
        self.option_type = option_type
        self.strike = strike
        self.expiry = expiry
        
        # Optional fields
        self.current_price = kwargs.get('current_price', entry_price)
        self.stop_loss = kwargs.get('stop_loss')
        self.profit_target = kwargs.get('profit_target')
        self.max_hold_time = kwargs.get('max_hold_time')
        
        # Greeks and risk metrics
        self.delta = kwargs.get('delta', 0.0)
        self.gamma = kwargs.get('gamma', 0.0)
        self.theta = kwargs.get('theta', 0.0)
        self.vega = kwargs.get('vega', 0.0)
        self.iv = kwargs.get('iv', 0.0)
        
        # Tracking
        self.exit_time = None
        self.exit_price = None
        self.exit_reason = None
        self.realized_pnl = None
        
        # Metadata
        self.metadata = kwargs.get('metadata', {})
    
    @property
    def is_open(self) -> bool:
        """Check if position is still open"""
        return self.exit_time is None
    
    @property
    def unrealized_pnl(self) -> float:
        """Calculate unrealized P&L"""
        if not self.is_open:
            return self.realized_pnl or 0.0
        
        price_diff = self.current_price - self.entry_price
        return price_diff * self.contracts * 100  # Contract multiplier
    
    @property
    def hold_time_minutes(self) -> int:
        """Get hold time in minutes"""
        end_time = self.exit_time or datetime.now()
        return int((end_time - self.entry_time).total_seconds() / 60)
    
    def update_price(self, new_price: float, **greeks):
        """Update current price and Greeks"""
        self.current_price = new_price
        
        # Update Greeks if provided
        for greek, value in greeks.items():
            if hasattr(self, greek):
                setattr(self, greek, value)
    
    def close_position(self, exit_price: float, exit_reason: str):
        """Close the position"""
        self.exit_time = datetime.now()
        self.exit_price = exit_price
        self.exit_reason = exit_reason
        
        price_diff = exit_price - self.entry_price
        self.realized_pnl = price_diff * self.contracts * 100
    
    def should_exit(self) -> tuple:
        """Check if position should be exited based on rules"""
        current_time = datetime.now()
        
        # Time-based exit
        if self.max_hold_time:
            if self.hold_time_minutes >= self.max_hold_time:
                return True, "MAX_HOLD_TIME"
        
        # Stop loss
        if self.stop_loss:
            if self.current_price <= self.stop_loss:
                return True, "STOP_LOSS"
        
        # Profit target
        if self.profit_target:
            if self.current_price >= self.profit_target:
                return True, "PROFIT_TARGET"
        
        # Market close (avoid overnight positions)
        market_close = current_time.replace(hour=16, minute=0, second=0, microsecond=0)
        if current_time >= market_close - timedelta(minutes=TRADING_CONFIG.MARKET_CLOSE_BUFFER_MINUTES):
            return True, "MARKET_CLOSE"
        
        return False, ""
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert position to dictionary"""
        return {
            'symbol': self.symbol,
            'strategy': self.strategy,
            'entry_time': self.entry_time.isoformat(),
            'exit_time': self.exit_time.isoformat() if self.exit_time else None,
            'contracts': self.contracts,
            'entry_price': self.entry_price,
            'exit_price': self.exit_price,
            'current_price': self.current_price,
            'option_type': self.option_type,
            'strike': self.strike,
            'expiry': self.expiry,
            'unrealized_pnl': self.unrealized_pnl,
            'realized_pnl': self.realized_pnl,
            'hold_time_minutes': self.hold_time_minutes,
            'exit_reason': self.exit_reason,
            'delta': self.delta,
            'gamma': self.gamma,
            'theta': self.theta,
            'vega': self.vega,
            'iv': self.iv,
            'metadata': self.metadata
        }

class BaseStrategy(ABC):
    """Abstract base class for all trading strategies"""
    
    def __init__(self, name: str, allocation: float):
        self.name = name
        self.allocation = allocation
        self.positions: List[Position] = []
        self.daily_pnl = 0.0
        self.trades_today = 0
        self.is_active = True
        
        # Performance tracking
        self.total_trades = 0
        self.winning_trades = 0
        self.total_pnl = 0.0
        self.max_drawdown = 0.0
        
        trading_logger.logger.info(f"Initialized {name} strategy with {allocation:.1%} allocation")
    
    @property
    def allocated_capital(self) -> float:
        """Get allocated capital for this strategy"""
        return TRADING_CONFIG.TOTAL_CAPITAL * self.allocation
    
    @property
    def open_positions(self) -> List[Position]:
        """Get list of open positions"""
        return [pos for pos in self.positions if pos.is_open]
    
    @property
    def closed_positions(self) -> List[Position]:
        """Get list of closed positions"""
        return [pos for pos in self.positions if not pos.is_open]
    
    @property
    def current_exposure(self) -> float:
        """Calculate current capital exposure"""
        exposure = 0.0
        for pos in self.open_positions:
            exposure += abs(pos.entry_price * pos.contracts * 100)
        return exposure
    
    @property
    def win_rate(self) -> float:
        """Calculate win rate"""
        if self.total_trades == 0:
            return 0.0
        return self.winning_trades / self.total_trades
    
    @abstractmethod
    def generate_signals(self, market_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate trading signals based on market data
        
        Args:
            market_data: Dictionary containing market data for analysis
            
        Returns:
            List of signal dictionaries
        """
        pass
    
    @abstractmethod
    def calculate_position_size(self, signal: Dict[str, Any], option_price: float) -> int:
        """
        Calculate position size for a given signal
        
        Args:
            signal: Trading signal dictionary
            option_price: Current option price
            
        Returns:
            Number of contracts to trade
        """
        pass
    
    @abstractmethod
    def get_entry_criteria(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get specific entry criteria for the signal
        
        Args:
            signal: Trading signal dictionary
            
        Returns:
            Dictionary with entry parameters
        """
        pass
    
    def can_trade(self) -> tuple:
        """Check if strategy can place new trades"""
        if not self.is_active:
            return False, "Strategy inactive"
        
        # Check if daily profit target reached
        if self.daily_pnl >= TRADING_CONFIG.DAILY_PROFIT_TARGET:
            return False, "Daily profit target reached"
        
        # Check if daily loss limit reached
        if self.daily_pnl <= -TRADING_CONFIG.MAX_DAILY_LOSS:
            return False, "Daily loss limit reached"
        
        # Check exposure limits
        if self.current_exposure >= self.allocated_capital * 0.8:  # 80% max exposure
            return False, "Maximum exposure reached"
        
        # Check market hours
        current_time = datetime.now()
        market_open = current_time.replace(hour=9, minute=30, second=0, microsecond=0)
        market_close = current_time.replace(hour=16, minute=0, second=0, microsecond=0)
        
        # Add buffer after market open
        trading_start = market_open + timedelta(minutes=TRADING_CONFIG.MARKET_OPEN_DELAY_MINUTES)
        trading_end = market_close - timedelta(minutes=TRADING_CONFIG.MARKET_CLOSE_BUFFER_MINUTES)
        
        if not (trading_start <= current_time <= trading_end):
            return False, "Outside trading hours"
        
        return True, "OK"
    
    def add_position(self, position: Position):
        """Add a new position to the strategy"""
        self.positions.append(position)
        self.trades_today += 1
        self.total_trades += 1
        
        trading_logger.log_trade(
            strategy=self.name,
            symbol=position.symbol,
            action="OPEN",
            quantity=position.contracts,
            price=position.entry_price,
            option_type=position.option_type,
            strike=position.strike,
            expiry=position.expiry,
            greeks={
                'delta': position.delta,
                'gamma': position.gamma,
                'theta': position.theta,
                'vega': position.vega,
                'iv': position.iv
            },
            reason="Strategy signal"
        )
    
    def close_position(self, position: Position, exit_price: float, reason: str):
        """Close a position"""
        position.close_position(exit_price, reason)
        
        # Update performance metrics
        if position.realized_pnl > 0:
            self.winning_trades += 1
        
        self.daily_pnl += position.realized_pnl
        self.total_pnl += position.realized_pnl
        
        trading_logger.log_trade(
            strategy=self.name,
            symbol=position.symbol,
            action="CLOSE",
            quantity=position.contracts,
            price=exit_price,
            option_type=position.option_type,
            strike=position.strike,
            expiry=position.expiry,
            pnl=position.realized_pnl,
            reason=reason
        )
    
    def update_positions(self, market_data: Dict[str, Any]):
        """Update all open positions with current market data"""
        for position in self.open_positions:
            # Update position with current market data
            symbol_data = market_data.get(position.symbol, {})
            if symbol_data:
                current_price = symbol_data.get('option_price', position.current_price)
                greeks = symbol_data.get('greeks', {})
                position.update_price(current_price, **greeks)
                
                # Check exit conditions
                should_exit, exit_reason = position.should_exit()
                if should_exit:
                    self.close_position(position, current_price, exit_reason)
    
    def reset_daily_metrics(self):
        """Reset daily performance metrics"""
        self.daily_pnl = 0.0
        self.trades_today = 0
        trading_logger.logger.info(f"Reset daily metrics for {self.name} strategy")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get strategy performance summary"""
        return {
            'name': self.name,
            'allocation': self.allocation,
            'daily_pnl': self.daily_pnl,
            'total_pnl': self.total_pnl,
            'trades_today': self.trades_today,
            'total_trades': self.total_trades,
            'win_rate': self.win_rate,
            'open_positions': len(self.open_positions),
            'current_exposure': self.current_exposure,
            'allocated_capital': self.allocated_capital,
            'is_active': self.is_active
        }
