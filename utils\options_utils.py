"""
Options pricing and Greeks calculation utilities
"""
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from scipy.stats import norm
import py_vollib.black_scholes as bs
import py_vollib.black_scholes.greeks.analytical as greeks
from py_vollib.black_scholes.implied_volatility import implied_volatility

class OptionsCalculator:
    """Black-Scholes options pricing and Greeks calculator"""
    
    @staticmethod
    def time_to_expiry(expiry_date: str, current_date: Optional[datetime] = None) -> float:
        """
        Calculate time to expiry in years
        
        Args:
            expiry_date: Expiry date in 'YYYY-MM-DD' format
            current_date: Current date (defaults to now)
        
        Returns:
            Time to expiry in years
        """
        if current_date is None:
            current_date = datetime.now()
        
        if isinstance(expiry_date, str):
            expiry = datetime.strptime(expiry_date, '%Y-%m-%d')
        else:
            expiry = expiry_date
        
        time_diff = expiry - current_date
        return max(time_diff.total_seconds() / (365.25 * 24 * 3600), 1/365.25)  # Min 1 day
    
    @staticmethod
    def black_scholes_price(spot: float, strike: float, time_to_expiry: float, 
                           risk_free_rate: float, volatility: float, 
                           option_type: str = 'call') -> float:
        """
        Calculate Black-Scholes option price
        
        Args:
            spot: Current stock price
            strike: Strike price
            time_to_expiry: Time to expiry in years
            risk_free_rate: Risk-free interest rate
            volatility: Implied volatility
            option_type: 'call' or 'put'
        
        Returns:
            Option price
        """
        try:
            if option_type.lower() == 'call':
                return bs.black_scholes('c', spot, strike, time_to_expiry, risk_free_rate, volatility)
            else:
                return bs.black_scholes('p', spot, strike, time_to_expiry, risk_free_rate, volatility)
        except Exception:
            return 0.0
    
    @staticmethod
    def calculate_greeks(spot: float, strike: float, time_to_expiry: float,
                        risk_free_rate: float, volatility: float,
                        option_type: str = 'call') -> Dict[str, float]:
        """
        Calculate all option Greeks
        
        Returns:
            Dictionary with delta, gamma, theta, vega, rho
        """
        try:
            flag = 'c' if option_type.lower() == 'call' else 'p'
            
            return {
                'delta': greeks.delta(flag, spot, strike, time_to_expiry, risk_free_rate, volatility),
                'gamma': greeks.gamma(flag, spot, strike, time_to_expiry, risk_free_rate, volatility),
                'theta': greeks.theta(flag, spot, strike, time_to_expiry, risk_free_rate, volatility),
                'vega': greeks.vega(flag, spot, strike, time_to_expiry, risk_free_rate, volatility),
                'rho': greeks.rho(flag, spot, strike, time_to_expiry, risk_free_rate, volatility)
            }
        except Exception:
            return {'delta': 0.0, 'gamma': 0.0, 'theta': 0.0, 'vega': 0.0, 'rho': 0.0}
    
    @staticmethod
    def implied_volatility_from_price(price: float, spot: float, strike: float,
                                     time_to_expiry: float, risk_free_rate: float,
                                     option_type: str = 'call') -> float:
        """
        Calculate implied volatility from option price
        
        Returns:
            Implied volatility
        """
        try:
            flag = 'c' if option_type.lower() == 'call' else 'p'
            return implied_volatility(price, spot, strike, time_to_expiry, risk_free_rate, flag)
        except Exception:
            return 0.20  # Default 20% IV if calculation fails

class OptionsChain:
    """Options chain analysis and filtering"""
    
    def __init__(self, chain_data: List[Dict]):
        """
        Initialize with options chain data
        
        Args:
            chain_data: List of option contracts with pricing and Greeks
        """
        self.chain_data = chain_data
        self.df = pd.DataFrame(chain_data) if chain_data else pd.DataFrame()
    
    def filter_by_liquidity(self, min_volume: int = 10, max_spread_pct: float = 0.05) -> 'OptionsChain':
        """Filter options by liquidity criteria"""
        if self.df.empty:
            return self
        
        # Calculate bid-ask spread percentage
        self.df['spread_pct'] = (self.df['ask'] - self.df['bid']) / self.df['ask']
        
        # Filter conditions
        liquid_options = self.df[
            (self.df['volume'] >= min_volume) &
            (self.df['spread_pct'] <= max_spread_pct) &
            (self.df['bid'] > 0) &
            (self.df['ask'] > 0)
        ]
        
        return OptionsChain(liquid_options.to_dict('records'))
    
    def filter_by_delta(self, min_delta: float = 0.20, max_delta: float = 0.80) -> 'OptionsChain':
        """Filter options by delta range"""
        if self.df.empty:
            return self
        
        filtered = self.df[
            (abs(self.df['delta']) >= min_delta) &
            (abs(self.df['delta']) <= max_delta)
        ]
        
        return OptionsChain(filtered.to_dict('records'))
    
    def filter_by_expiry(self, max_days: int = 7) -> 'OptionsChain':
        """Filter options by days to expiry"""
        if self.df.empty:
            return self
        
        current_date = datetime.now()
        
        def days_to_expiry(expiry_str):
            try:
                expiry = datetime.strptime(expiry_str, '%Y-%m-%d')
                return (expiry - current_date).days
            except:
                return 999  # Invalid date, filter out
        
        self.df['days_to_expiry'] = self.df['expiry'].apply(days_to_expiry)
        filtered = self.df[self.df['days_to_expiry'] <= max_days]
        
        return OptionsChain(filtered.to_dict('records'))
    
    def get_atm_options(self, spot_price: float, tolerance: float = 0.05) -> List[Dict]:
        """Get at-the-money options within tolerance"""
        if self.df.empty:
            return []
        
        # Find strikes closest to spot price
        self.df['distance_from_atm'] = abs(self.df['strike'] - spot_price) / spot_price
        atm_options = self.df[self.df['distance_from_atm'] <= tolerance]
        
        return atm_options.to_dict('records')
    
    def get_best_gamma_options(self, min_gamma: float = 0.01) -> List[Dict]:
        """Get options with highest gamma for scalping"""
        if self.df.empty:
            return []
        
        high_gamma = self.df[self.df['gamma'] >= min_gamma]
        return high_gamma.sort_values('gamma', ascending=False).to_dict('records')

class StrategyPricer:
    """Price complex options strategies"""
    
    @staticmethod
    def straddle_price(call_price: float, put_price: float) -> Dict[str, float]:
        """Calculate straddle pricing"""
        return {
            'total_cost': call_price + put_price,
            'call_cost': call_price,
            'put_cost': put_price,
            'breakeven_up': 0,  # Will be calculated with strike
            'breakeven_down': 0
        }
    
    @staticmethod
    def strangle_price(call_price: float, put_price: float, 
                      call_strike: float, put_strike: float) -> Dict[str, float]:
        """Calculate strangle pricing"""
        total_cost = call_price + put_price
        return {
            'total_cost': total_cost,
            'call_cost': call_price,
            'put_cost': put_price,
            'breakeven_up': call_strike + total_cost,
            'breakeven_down': put_strike - total_cost
        }
    
    @staticmethod
    def iron_condor_price(short_call_price: float, short_put_price: float,
                         long_call_price: float, long_put_price: float) -> Dict[str, float]:
        """Calculate iron condor pricing"""
        credit_received = short_call_price + short_put_price - long_call_price - long_put_price
        return {
            'credit_received': credit_received,
            'max_profit': credit_received,
            'short_call_cost': short_call_price,
            'short_put_cost': short_put_price,
            'long_call_cost': long_call_price,
            'long_put_cost': long_put_price
        }
    
    @staticmethod
    def credit_spread_price(short_price: float, long_price: float,
                           strike_width: float) -> Dict[str, float]:
        """Calculate credit spread pricing"""
        credit_received = short_price - long_price
        max_loss = strike_width - credit_received
        
        return {
            'credit_received': credit_received,
            'max_profit': credit_received,
            'max_loss': max_loss,
            'profit_probability': credit_received / strike_width if strike_width > 0 else 0
        }

class RiskCalculator:
    """Calculate position risk metrics"""
    
    @staticmethod
    def position_size_for_risk(account_value: float, risk_per_trade: float,
                              option_price: float, max_loss_per_contract: float) -> int:
        """
        Calculate position size based on risk management
        
        Args:
            account_value: Total account value
            risk_per_trade: Risk percentage per trade (e.g., 0.02 for 2%)
            option_price: Price per option contract
            max_loss_per_contract: Maximum loss per contract
        
        Returns:
            Number of contracts to trade
        """
        max_risk_dollars = account_value * risk_per_trade
        
        # For long options, max loss is premium paid
        # For short options, max loss could be much higher
        risk_per_contract = max(option_price * 100, max_loss_per_contract)  # *100 for contract multiplier
        
        if risk_per_contract <= 0:
            return 0
        
        contracts = int(max_risk_dollars / risk_per_contract)
        return max(1, contracts)  # At least 1 contract if we're trading
    
    @staticmethod
    def portfolio_delta(positions: List[Dict]) -> float:
        """Calculate total portfolio delta"""
        total_delta = 0
        for position in positions:
            contracts = position.get('contracts', 0)
            delta = position.get('delta', 0)
            total_delta += contracts * delta * 100  # Contract multiplier
        
        return total_delta
    
    @staticmethod
    def portfolio_gamma(positions: List[Dict]) -> float:
        """Calculate total portfolio gamma"""
        total_gamma = 0
        for position in positions:
            contracts = position.get('contracts', 0)
            gamma = position.get('gamma', 0)
            total_gamma += contracts * gamma * 100  # Contract multiplier
        
        return total_gamma
    
    @staticmethod
    def portfolio_theta(positions: List[Dict]) -> float:
        """Calculate total portfolio theta (daily decay)"""
        total_theta = 0
        for position in positions:
            contracts = position.get('contracts', 0)
            theta = position.get('theta', 0)
            total_theta += contracts * theta * 100  # Contract multiplier
        
        return total_theta
    
    @staticmethod
    def max_loss_estimate(positions: List[Dict], underlying_move_pct: float = 0.05) -> float:
        """
        Estimate maximum loss for a given underlying move
        
        Args:
            positions: List of position dictionaries
            underlying_move_pct: Percentage move to stress test (e.g., 0.05 for 5%)
        
        Returns:
            Estimated maximum loss
        """
        total_loss = 0
        
        for position in positions:
            contracts = position.get('contracts', 0)
            delta = position.get('delta', 0)
            gamma = position.get('gamma', 0)
            current_price = position.get('current_price', 0)
            
            # Estimate P&L using delta and gamma approximation
            spot_move = current_price * underlying_move_pct
            delta_pnl = delta * spot_move * contracts * 100
            gamma_pnl = 0.5 * gamma * (spot_move ** 2) * contracts * 100
            
            position_pnl = delta_pnl + gamma_pnl
            total_loss += min(0, position_pnl)  # Only count losses
        
        return abs(total_loss)

# Utility functions for common calculations
def calculate_option_metrics(spot: float, strike: float, expiry: str, 
                           volatility: float, risk_free_rate: float = 0.05,
                           option_type: str = 'call') -> Dict[str, float]:
    """
    Calculate comprehensive option metrics
    
    Returns:
        Dictionary with price, Greeks, and other metrics
    """
    calc = OptionsCalculator()
    tte = calc.time_to_expiry(expiry)
    
    price = calc.black_scholes_price(spot, strike, tte, risk_free_rate, volatility, option_type)
    greeks_dict = calc.calculate_greeks(spot, strike, tte, risk_free_rate, volatility, option_type)
    
    return {
        'theoretical_price': price,
        'time_to_expiry': tte,
        'moneyness': spot / strike,
        'intrinsic_value': max(0, spot - strike) if option_type.lower() == 'call' else max(0, strike - spot),
        'time_value': price - max(0, spot - strike if option_type.lower() == 'call' else strike - spot),
        **greeks_dict
    }

def find_optimal_strikes(spot_price: float, target_delta: float = 0.50, 
                        strike_increment: float = 1.0) -> Tuple[float, float]:
    """
    Find optimal call and put strikes for a given delta target
    
    Returns:
        Tuple of (call_strike, put_strike)
    """
    # For ATM, strikes should be close to spot
    call_strike = round(spot_price / strike_increment) * strike_increment
    put_strike = call_strike
    
    # Adjust for target delta if not ATM
    if target_delta != 0.50:
        if target_delta > 0.50:  # ITM call
            call_strike = spot_price * (1 - (0.50 - target_delta))
        else:  # OTM call
            call_strike = spot_price * (1 + (target_delta - 0.50))
        
        call_strike = round(call_strike / strike_increment) * strike_increment
        put_strike = call_strike
    
    return call_strike, put_strike
