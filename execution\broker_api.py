"""
Broker API integration for order execution
"""
import alpaca_trade_api as tradeapi
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional
import asyncio
import json

from utils.logger import trading_logger
from config import TRADING_CONFIG

class BrokerAPI:
    """Alpaca broker API integration"""
    
    def __init__(self):
        self.api = tradeapi.REST(
            TRADING_CONFIG.ALPACA_API_KEY,
            TRADING_CONFIG.ALPACA_SECRET_KEY,
            TRADING_CONFIG.ALPACA_BASE_URL,
            api_version='v2'
        )
        
        self.account = None
        self.positions = {}
        self.orders = {}
        
        # Order tracking
        self.pending_orders = {}
        self.filled_orders = {}
        
    async def initialize(self):
        """Initialize broker connection and get account info"""
        try:
            self.account = self.api.get_account()
            trading_logger.logger.info(f"Connected to Alpaca - Account: {self.account.id}")
            trading_logger.logger.info(f"Buying Power: ${float(self.account.buying_power):,.2f}")
            trading_logger.logger.info(f"Portfolio Value: ${float(self.account.portfolio_value):,.2f}")
            
            # Get current positions
            await self._update_positions()
            
            return True
            
        except Exception as e:
            trading_logger.log_error(e, "Failed to initialize broker API")
            return False
    
    async def _update_positions(self):
        """Update current positions"""
        try:
            positions = self.api.list_positions()
            self.positions = {}
            
            for position in positions:
                self.positions[position.symbol] = {
                    'symbol': position.symbol,
                    'qty': int(position.qty),
                    'side': position.side,
                    'market_value': float(position.market_value),
                    'avg_entry_price': float(position.avg_entry_price),
                    'unrealized_pl': float(position.unrealized_pl),
                    'unrealized_plpc': float(position.unrealized_plpc)
                }
                
        except Exception as e:
            trading_logger.log_error(e, "Error updating positions")
    
    def get_account_info(self) -> Dict[str, Any]:
        """Get current account information"""
        try:
            account = self.api.get_account()
            return {
                'buying_power': float(account.buying_power),
                'portfolio_value': float(account.portfolio_value),
                'cash': float(account.cash),
                'day_trade_count': int(account.day_trade_count),
                'pattern_day_trader': account.pattern_day_trader,
                'trading_blocked': account.trading_blocked,
                'account_blocked': account.account_blocked
            }
        except Exception as e:
            trading_logger.log_error(e, "Error getting account info")
            return {}
    
    async def place_option_order(self, symbol: str, option_symbol: str, qty: int, 
                                side: str, order_type: str = 'limit', 
                                limit_price: float = None, **kwargs) -> Optional[str]:
        """
        Place an options order
        
        Args:
            symbol: Underlying symbol
            option_symbol: Full option symbol (e.g., 'AAPL230120C00150000')
            qty: Number of contracts
            side: 'buy' or 'sell'
            order_type: 'market' or 'limit'
            limit_price: Limit price for limit orders
            
        Returns:
            Order ID if successful, None if failed
        """
        try:
            # Validate order parameters
            if not self._validate_order_params(symbol, qty, side, order_type, limit_price):
                return None
            
            # Create order request
            order_request = {
                'symbol': option_symbol,
                'qty': qty,
                'side': side,
                'type': order_type,
                'time_in_force': 'day',  # Day orders only for intraday trading
                'class': 'option'
            }
            
            if order_type == 'limit' and limit_price:
                order_request['limit_price'] = limit_price
            
            # Add any additional parameters
            order_request.update(kwargs)
            
            # Submit order
            order = self.api.submit_order(**order_request)
            
            # Track the order
            self.pending_orders[order.id] = {
                'order_id': order.id,
                'symbol': symbol,
                'option_symbol': option_symbol,
                'qty': qty,
                'side': side,
                'order_type': order_type,
                'limit_price': limit_price,
                'status': order.status,
                'submitted_at': order.submitted_at,
                'filled_qty': 0,
                'filled_avg_price': 0
            }
            
            trading_logger.logger.info(
                f"Submitted {side} order for {qty} {option_symbol} at ${limit_price} - Order ID: {order.id}"
            )
            
            return order.id
            
        except Exception as e:
            trading_logger.log_error(e, f"Error placing option order for {symbol}")
            return None
    
    def _validate_order_params(self, symbol: str, qty: int, side: str, 
                              order_type: str, limit_price: float = None) -> bool:
        """Validate order parameters"""
        
        # Check quantity
        if qty <= 0 or qty > 100:  # Reasonable limits
            trading_logger.logger.error(f"Invalid quantity: {qty}")
            return False
        
        # Check side
        if side not in ['buy', 'sell']:
            trading_logger.logger.error(f"Invalid side: {side}")
            return False
        
        # Check order type
        if order_type not in ['market', 'limit']:
            trading_logger.logger.error(f"Invalid order type: {order_type}")
            return False
        
        # Check limit price for limit orders
        if order_type == 'limit':
            if not limit_price or limit_price <= 0:
                trading_logger.logger.error(f"Invalid limit price: {limit_price}")
                return False
        
        # Check account status
        account_info = self.get_account_info()
        if account_info.get('trading_blocked', True):
            trading_logger.logger.error("Trading is blocked")
            return False
        
        return True
    
    async def place_spread_order(self, legs: List[Dict[str, Any]], 
                                order_type: str = 'limit', 
                                limit_price: float = None) -> Optional[str]:
        """
        Place a multi-leg spread order
        
        Args:
            legs: List of order legs, each with symbol, qty, side
            order_type: 'market' or 'limit'
            limit_price: Net credit/debit for the spread
            
        Returns:
            Order ID if successful
        """
        try:
            # Alpaca doesn't support multi-leg orders yet in their API
            # We'll need to place individual orders and manage them
            # This is a simplified implementation
            
            order_ids = []
            
            for leg in legs:
                order_id = await self.place_option_order(
                    symbol=leg['underlying_symbol'],
                    option_symbol=leg['option_symbol'],
                    qty=leg['qty'],
                    side=leg['side'],
                    order_type=order_type,
                    limit_price=leg.get('limit_price', limit_price)
                )
                
                if order_id:
                    order_ids.append(order_id)
                else:
                    # If any leg fails, cancel the others
                    for cancel_id in order_ids:
                        await self.cancel_order(cancel_id)
                    return None
            
            # Create a spread order tracking entry
            spread_id = f"SPREAD_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self.pending_orders[spread_id] = {
                'order_id': spread_id,
                'type': 'spread',
                'legs': order_ids,
                'status': 'submitted',
                'submitted_at': datetime.now().isoformat()
            }
            
            trading_logger.logger.info(f"Submitted spread order with {len(legs)} legs - Spread ID: {spread_id}")
            
            return spread_id
            
        except Exception as e:
            trading_logger.log_error(e, "Error placing spread order")
            return None
    
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel an order"""
        try:
            self.api.cancel_order(order_id)
            
            if order_id in self.pending_orders:
                self.pending_orders[order_id]['status'] = 'cancelled'
            
            trading_logger.logger.info(f"Cancelled order {order_id}")
            return True
            
        except Exception as e:
            trading_logger.log_error(e, f"Error cancelling order {order_id}")
            return False
    
    async def get_order_status(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Get order status"""
        try:
            order = self.api.get_order(order_id)
            
            order_info = {
                'order_id': order.id,
                'status': order.status,
                'filled_qty': int(order.filled_qty) if order.filled_qty else 0,
                'filled_avg_price': float(order.filled_avg_price) if order.filled_avg_price else 0,
                'submitted_at': order.submitted_at,
                'filled_at': order.filled_at,
                'cancelled_at': order.cancelled_at,
                'expired_at': order.expired_at
            }
            
            # Update our tracking
            if order_id in self.pending_orders:
                self.pending_orders[order_id].update(order_info)
                
                # Move to filled orders if complete
                if order.status in ['filled', 'partially_filled']:
                    self.filled_orders[order_id] = self.pending_orders[order_id]
                    if order.status == 'filled':
                        del self.pending_orders[order_id]
            
            return order_info
            
        except Exception as e:
            trading_logger.log_error(e, f"Error getting order status for {order_id}")
            return None
    
    async def update_all_orders(self):
        """Update status of all pending orders"""
        for order_id in list(self.pending_orders.keys()):
            await self.get_order_status(order_id)
    
    def get_position(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get position for a symbol"""
        return self.positions.get(symbol)
    
    def get_all_positions(self) -> Dict[str, Any]:
        """Get all current positions"""
        return self.positions.copy()
    
    async def close_position(self, symbol: str, qty: int = None) -> Optional[str]:
        """
        Close a position
        
        Args:
            symbol: Symbol to close
            qty: Quantity to close (None for full position)
            
        Returns:
            Order ID if successful
        """
        try:
            position = self.get_position(symbol)
            if not position:
                trading_logger.logger.warning(f"No position found for {symbol}")
                return None
            
            current_qty = position['qty']
            close_qty = qty if qty else abs(current_qty)
            
            # Determine side (opposite of current position)
            side = 'sell' if current_qty > 0 else 'buy'
            
            # For options, we need the full option symbol
            # This is simplified - in practice, you'd need to track the exact option symbol
            if symbol.endswith(('C', 'P')):  # Option symbol
                order_id = await self.place_option_order(
                    symbol=symbol[:symbol.find('2')],  # Extract underlying
                    option_symbol=symbol,
                    qty=close_qty,
                    side=side,
                    order_type='market'  # Use market order for quick exit
                )
            else:  # Stock position
                order_id = await self.place_stock_order(
                    symbol=symbol,
                    qty=close_qty,
                    side=side,
                    order_type='market'
                )
            
            if order_id:
                trading_logger.logger.info(f"Submitted close order for {close_qty} {symbol}")
            
            return order_id
            
        except Exception as e:
            trading_logger.log_error(e, f"Error closing position for {symbol}")
            return None
    
    async def place_stock_order(self, symbol: str, qty: int, side: str, 
                               order_type: str = 'market', limit_price: float = None) -> Optional[str]:
        """Place a stock order (for hedging in gamma scalping)"""
        try:
            order_request = {
                'symbol': symbol,
                'qty': qty,
                'side': side,
                'type': order_type,
                'time_in_force': 'day'
            }
            
            if order_type == 'limit' and limit_price:
                order_request['limit_price'] = limit_price
            
            order = self.api.submit_order(**order_request)
            
            trading_logger.logger.info(
                f"Submitted {side} order for {qty} {symbol} - Order ID: {order.id}"
            )
            
            return order.id
            
        except Exception as e:
            trading_logger.log_error(e, f"Error placing stock order for {symbol}")
            return None
    
    def is_market_open(self) -> bool:
        """Check if market is open"""
        try:
            clock = self.api.get_clock()
            return clock.is_open
        except:
            return False
    
    def get_market_hours(self) -> Dict[str, Any]:
        """Get market hours"""
        try:
            clock = self.api.get_clock()
            return {
                'is_open': clock.is_open,
                'next_open': clock.next_open.isoformat(),
                'next_close': clock.next_close.isoformat(),
                'timestamp': clock.timestamp.isoformat()
            }
        except Exception as e:
            trading_logger.log_error(e, "Error getting market hours")
            return {}

# Global broker API instance
broker_api = BrokerAPI()

async def initialize_broker():
    """Initialize broker connection"""
    return await broker_api.initialize()

def get_account_info():
    """Get account information"""
    return broker_api.get_account_info()

async def place_option_order(*args, **kwargs):
    """Place option order"""
    return await broker_api.place_option_order(*args, **kwargs)

async def place_spread_order(*args, **kwargs):
    """Place spread order"""
    return await broker_api.place_spread_order(*args, **kwargs)

def is_market_open():
    """Check if market is open"""
    return broker_api.is_market_open()
